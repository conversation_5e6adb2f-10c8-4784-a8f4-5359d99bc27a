const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Application = require('../models/Application');
const {
  authenticate,
  requireEmailVerification
} = require('../middleware/auth');
const path = require('path');
const fs = require('fs');

const router = express.Router();

// Validation middleware
const validateApplication = [
  body('contactInfo.phone')
    .notEmpty()
    .withMessage('Contact phone is required')
    .isMobilePhone('any')
    .withMessage('Please provide a valid phone number'),
  body('files').isArray({ min: 1 }).withMessage('At least one file is required'),
  body('files.*.originalName').notEmpty().withMessage('File original name is required'),
  body('files.*.filename').notEmpty().withMessage('File filename is required'),
  body('files.*.path').notEmpty().withMessage('File path is required'),
  body('analysisType')
    .optional()
    .isIn(['protein_complex', 'protein_interaction', 'structure_prediction', 'drug_target'])
    .withMessage('Invalid analysis type'),
  body('parameters.algorithm')
    .optional()
    .isIn(['alphafold', 'rosetta', 'modeller', 'custom'])
    .withMessage('Invalid algorithm'),
  body('parameters.confidence')
    .optional()
    .isFloat({ min: 0.1, max: 1.0 })
    .withMessage('Confidence must be between 0.1 and 1.0')
];

// Submit new application
router.post('/', authenticate, requireEmailVerification, validateApplication, async (req, res) => {
  try {
    console.log('📋 Application submission request received');
    console.log('👤 User:', req.user.id, req.user.email);
    console.log('📊 Request body:', JSON.stringify(req.body, null, 2));
    console.log('📁 Upload path:', process.env.UPLOAD_PATH || './server/uploads');
    console.log(
      '📂 User directory will be:',
      path.join(process.env.UPLOAD_PATH || './server/uploads', req.user.id.toString())
    );

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('❌ Validation errors:', errors.array());
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { contactInfo, files, analysisType, parameters, notes } = req.body;

    // 验证文件数组
    if (!files || !Array.isArray(files) || files.length === 0) {
      return res.status(400).json({
        error: 'No files provided',
        message: 'Please upload at least one file before submitting the application.'
      });
    }

    // Verify that all files exist and belong to the user
    const userDir = path.join(process.env.UPLOAD_PATH || './server/uploads', req.user.id.toString());
    console.log('📂 User directory:', userDir);

    for (const file of files) {
      // 构建文件路径，支持相对路径和绝对路径
      let filePath;
      if (file.path && path.isAbsolute(file.path)) {
        filePath = file.path;
      } else {
        filePath = path.join(userDir, file.filename);
      }

      console.log(`🔍 Checking file: ${file.originalName}`);
      console.log(`   Expected path: ${filePath}`);

      if (!fs.existsSync(filePath)) {
        console.log(`❌ File not found: ${filePath}`);
        return res.status(400).json({
          error: 'File not found',
          message: `File ${file.originalName} not found. Please upload the file first.`,
          debug: {
            expectedPath: filePath,
            userDir: userDir,
            filename: file.filename,
            providedPath: file.path
          }
        });
      }

      console.log(`✅ File found: ${filePath}`);
    }

    // Create new application
    const application = await Application.create({
      userId: req.user.id,
      contactPhone: contactInfo.phone,
      contactEmail: req.user.email,
      files: files.map((file) => ({
        ...file,
        uploadedAt: new Date(),
        isProcessed: false
      })),
      analysisType: analysisType || 'protein_complex',
      parameters: {
        algorithm: parameters?.algorithm || 'alphafold',
        confidence: parameters?.confidence || 0.7,
        maxIterations: parameters?.maxIterations || 1000,
        customParameters: parameters?.customParameters || {}
      },
      notes: notes || '',
      priority: req.user.subscriptionPlan === 'premium' ? 'high' : 'normal'
    });

    // Calculate estimated cost based on files and analysis type
    const baseCost = analysisType === 'drug_target' ? 50 : 30;
    const fileCost = files.length * 10;
    application.estimatedCost = baseCost + fileCost;
    await application.save();

    console.log(`✅ Application created successfully: ${application.applicationId}`);
    console.log(`💰 Estimated cost: $${application.estimatedCost}`);

    res.status(201).json({
      message: 'Application submitted successfully',
      application: {
        id: application.id,
        applicationId: application.applicationId,
        status: application.status,
        analysisType: application.analysisType,
        estimatedCost: application.estimatedCost,
        createdAt: application.createdAt,
        files: application.files.map((file) => ({
          originalName: file.originalName,
          size: file.size,
          fileType: file.fileType
        }))
      }
    });
  } catch (error) {
    console.error('❌ Application submission error:', error);
    console.error('📊 Error stack:', error.stack);
    console.error('👤 User context:', req.user?.id, req.user?.email);
    console.error('📋 Request data:', JSON.stringify(req.body, null, 2));
    res.status(500).json({
      error: 'Submission failed',
      message: 'An error occurred while submitting the application',
      debug: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Get user's applications
router.get(
  '/',
  authenticate,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('status')
      .optional()
      .isIn(['pending', 'processing', 'completed', 'failed', 'cancelled'])
      .withMessage('Invalid status'),
    query('analysisType')
      .optional()
      .isIn(['protein_complex', 'protein_interaction', 'structure_prediction', 'drug_target'])
      .withMessage('Invalid analysis type')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      // Build filter
      const where = {
        userId: req.user.id,
        isDeleted: false
      };

      if (req.query.status) {
        where.status = req.query.status;
      }

      if (req.query.analysisType) {
        where.analysisType = req.query.analysisType;
      }

      // Get applications with pagination
      const { count: totalCount, rows: applications } = await Application.findAndCountAll({
        where,
        offset: skip,
        limit,
        order: [['createdAt', 'DESC']]
        // Note: Report association would need to be set up in models
      });

      const totalPages = Math.ceil(totalCount / limit);

      // Format response
      const formattedApplications = applications.map((app) => ({
        id: app.id,
        applicationId: app.applicationId,
        status: app.status,
        analysisType: app.analysisType,
        progress: app.progress,
        estimatedCost: app.estimatedCost,
        actualCost: app.actualCost,
        createdAt: app.createdAt,
        processingStartedAt: app.processingStartedAt,
        processingCompletedAt: app.processingCompletedAt,
        files: app.files.map((file) => ({
          originalName: file.originalName,
          size: file.size,
          fileType: file.fileType
        })),
        // Report files information
        previewReportFile: app.previewReportFile
          ? typeof app.previewReportFile === 'string'
            ? app.previewReportFile.startsWith('{') || app.previewReportFile.startsWith('[')
              ? JSON.parse(app.previewReportFile)
              : null
            : app.previewReportFile
          : null,
        fullReportFile: app.fullReportFile
          ? typeof app.fullReportFile === 'string'
            ? app.fullReportFile.startsWith('{') || app.fullReportFile.startsWith('[')
              ? JSON.parse(app.fullReportFile)
              : null
            : app.fullReportFile
          : null,
        reportPrice: app.reportPrice,
        reportPaymentStatus: app.reportPaymentStatus,
        report: app.report
          ? {
            id: app.report.id,
            reportId: app.report.reportId,
            status: app.report.status,
            generatedAt: app.report.generatedAt
          }
          : null
      }));

      res.json({
        applications: formattedApplications,
        pagination: {
          currentPage: page,
          totalPages: totalPages,
          totalCount: totalCount,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      });
    } catch (error) {
      console.error('Get applications error:', error);
      res.status(500).json({
        error: 'Failed to retrieve applications',
        message: 'An error occurred while retrieving applications'
      });
    }
  }
);

// Get specific application
router.get('/:id', authenticate, async (req, res) => {
  try {
    const application = await Application.findOne({
      where: {
        id: req.params.id,
        userId: req.user.id,
        isDeleted: false
      }
      // Note: Report association would need to be set up in models
    });

    if (!application) {
      return res.status(404).json({
        error: 'Application not found',
        message: 'The requested application does not exist or you do not have access to it'
      });
    }

    res.json({
      success: true,
      application: {
        id: application.id,
        applicationId: application.applicationId,
        title: application.title || `蛋白质分析申请 ${application.applicationId}`,
        status: application.status,
        analysisType: application.analysisType,
        parameters: application.parameters,
        progress: application.progress,
        results: application.results,
        estimatedCost: application.estimatedCost,
        actualCost: application.actualCost,
        priority: application.priority,
        notes: application.notes,
        createdAt: application.createdAt,
        processingStartedAt: application.processingStartedAt,
        processingCompletedAt: application.processingCompletedAt,
        files: application.files,
        // Report files information
        previewReportFile: application.previewReportFile
          ? typeof application.previewReportFile === 'string'
            ? application.previewReportFile.startsWith('{') ||
              application.previewReportFile.startsWith('[')
              ? JSON.parse(application.previewReportFile)
              : null
            : application.previewReportFile
          : null,
        fullReportFile: application.fullReportFile
          ? typeof application.fullReportFile === 'string'
            ? application.fullReportFile.startsWith('{') ||
              application.fullReportFile.startsWith('[')
              ? JSON.parse(application.fullReportFile)
              : null
            : application.fullReportFile
          : null,
        reportPrice: application.reportPrice,
        reportPaymentStatus: application.reportPaymentStatus,
        report: application.report
      }
    });
  } catch (error) {
    console.error('Get application error:', error);
    res.status(500).json({
      error: 'Failed to retrieve application',
      message: 'An error occurred while retrieving the application'
    });
  }
});

// Update application (limited fields)
router.patch(
  '/:id',
  authenticate,
  [
    body('notes').optional().isString().withMessage('Notes must be a string'),
    body('priority')
      .optional()
      .isIn(['low', 'normal', 'high', 'urgent'])
      .withMessage('Invalid priority')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const application = await Application.findOne({
        where: {
          id: req.params.id,
          userId: req.user.id,
          isDeleted: false
        }
      });

      if (!application) {
        return res.status(404).json({
          error: 'Application not found',
          message: 'The requested application does not exist or you do not have access to it'
        });
      }

      // Only allow updates if application is not processing or completed
      if (['processing', 'completed'].includes(application.status)) {
        return res.status(400).json({
          error: 'Cannot update application',
          message: 'Cannot update application that is processing or completed'
        });
      }

      // Update allowed fields
      const allowedUpdates = ['notes', 'priority'];
      const updates = {};

      allowedUpdates.forEach((field) => {
        if (req.body[field] !== undefined) {
          updates[field] = req.body[field];
        }
      });

      // Premium users can set higher priority
      if (
        updates.priority &&
        updates.priority === 'urgent' &&
        req.user.subscriptionPlan !== 'premium'
      ) {
        return res.status(403).json({
          error: 'Premium subscription required',
          message: 'Urgent priority is only available for premium subscribers'
        });
      }

      await application.update(updates);

      res.json({
        message: 'Application updated successfully',
        application: {
          id: application.id,
          applicationId: application.applicationId,
          status: application.status,
          priority: application.priority,
          notes: application.notes,
          updatedAt: application.updatedAt
        }
      });
    } catch (error) {
      console.error('Update application error:', error);
      res.status(500).json({
        error: 'Update failed',
        message: 'An error occurred while updating the application'
      });
    }
  }
);

// Cancel application
router.post('/:id/cancel', authenticate, async (req, res) => {
  try {
    const application = await Application.findOne({
      where: {
        id: req.params.id,
        userId: req.user.id,
        isDeleted: false
      }
    });

    if (!application) {
      return res.status(404).json({
        error: 'Application not found',
        message: 'The requested application does not exist or you do not have access to it'
      });
    }

    // Only allow cancellation if application is pending
    if (application.status !== 'pending') {
      return res.status(400).json({
        error: 'Cannot cancel application',
        message: 'Only pending applications can be cancelled'
      });
    }

    application.status = 'cancelled';
    await application.save();

    res.json({
      message: 'Application cancelled successfully',
      application: {
        id: application.id,
        applicationId: application.applicationId,
        status: application.status
      }
    });
  } catch (error) {
    console.error('Cancel application error:', error);
    res.status(500).json({
      error: 'Cancellation failed',
      message: 'An error occurred while cancelling the application'
    });
  }
});

// Delete application (soft delete)
router.delete('/:id', authenticate, async (req, res) => {
  try {
    const application = await Application.findOne({
      where: {
        id: req.params.id,
        userId: req.user.id,
        isDeleted: false
      }
    });

    if (!application) {
      return res.status(404).json({
        error: 'Application not found',
        message: 'The requested application does not exist or you do not have access to it'
      });
    }

    // Only allow deletion if application is not processing
    if (application.status === 'processing') {
      return res.status(400).json({
        error: 'Cannot delete application',
        message: 'Cannot delete application that is currently processing'
      });
    }

    application.isDeleted = true;
    await application.save();

    res.json({
      message: 'Application deleted successfully'
    });
  } catch (error) {
    console.error('Delete application error:', error);
    res.status(500).json({
      error: 'Deletion failed',
      message: 'An error occurred while deleting the application'
    });
  }
});

// Get application statistics
router.get('/stats/summary', authenticate, async (req, res) => {
  try {
    const { Sequelize } = require('sequelize');
    const userId = req.user.id;

    const stats = await Application.findAll({
      where: {
        userId: userId,
        isDeleted: false
      },
      attributes: [
        'status',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count'],
        [Sequelize.fn('SUM', Sequelize.col('actualCost')), 'totalCost']
      ],
      group: ['status'],
      raw: true
    });

    const summary = {
      total: 0,
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
      cancelled: 0,
      totalCost: 0
    };

    stats.forEach((stat) => {
      summary[stat.status] = parseInt(stat.count);
      summary.total += parseInt(stat.count);
      summary.totalCost += parseFloat(stat.totalCost) || 0;
    });

    res.json({ stats: summary });
  } catch (error) {
    console.error('Get application stats error:', error);
    res.status(500).json({
      error: 'Failed to retrieve statistics',
      message: 'An error occurred while retrieving application statistics'
    });
  }
});

// Download report files (user endpoint)
router.get('/:id/download/:reportType', authenticate, async (req, res) => {
  try {
    const { id, reportType } = req.params;

    // Validate report type
    if (!['preview', 'full'].includes(reportType)) {
      return res.status(400).json({
        error: 'Invalid report type',
        message: 'Report type must be either "preview" or "full"'
      });
    }

    // Find the application
    const application = await Application.findOne({
      where: {
        id: id,
        userId: req.user.id,
        isDeleted: false
      }
    });

    if (!application) {
      return res.status(404).json({
        error: 'Application not found',
        message: 'The requested application does not exist or you do not have access to it'
      });
    }

    // Check if application is completed
    if (application.status !== 'completed') {
      return res.status(400).json({
        error: 'Application not completed',
        message: 'Reports are only available for completed applications'
      });
    }

    // Get the appropriate report file
    let reportFile;
    if (reportType === 'preview') {
      reportFile = application.previewReportFile
        ? typeof application.previewReportFile === 'string'
          ? application.previewReportFile.startsWith('{') ||
            application.previewReportFile.startsWith('[')
            ? JSON.parse(application.previewReportFile)
            : null
          : application.previewReportFile
        : null;
    } else {
      reportFile = application.fullReportFile
        ? typeof application.fullReportFile === 'string'
          ? application.fullReportFile.startsWith('{') || application.fullReportFile.startsWith('[')
            ? JSON.parse(application.fullReportFile)
            : null
          : application.fullReportFile
        : null;

      // Check payment status for full report
      if (
        reportFile &&
        application.reportPaymentStatus !== 'paid' &&
        application.reportPaymentStatus !== 'free'
      ) {
        return res.status(403).json({
          error: 'Payment required',
          message: 'Full report requires payment. Please complete payment first.',
          paymentStatus: application.reportPaymentStatus,
          price: application.reportPrice
        });
      }
    }

    if (!reportFile) {
      return res.status(404).json({
        error: 'Report file not found',
        message: `${reportType === 'preview' ? 'Preview' : 'Full'} report is not available for this application`
      });
    }

    // Construct file path using the stored path
    // The path is stored relative to project root
    const filePath = path.join(__dirname, '..', reportFile.path.replace(/\\/g, '/'));

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        error: 'File not found',
        message: 'The report file could not be found on the server'
      });
    }

    // Set appropriate headers
    res.setHeader('Content-Disposition', `attachment; filename="${reportFile.originalName}"`);
    res.setHeader('Content-Type', reportFile.mimetype || 'application/octet-stream');

    // Send file
    res.sendFile(filePath);
  } catch (error) {
    console.error('Download report error:', error);
    res.status(500).json({
      error: 'Download failed',
      message: 'An error occurred while downloading the report'
    });
  }
});

module.exports = router;
