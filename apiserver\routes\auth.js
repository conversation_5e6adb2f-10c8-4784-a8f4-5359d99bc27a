const express = require('express');
const { body, validationResult } = require('express-validator');
const bcrypt = require('bcryptjs');
const { User } = require('../models');
const { generateToken, authenticate } = require('../middleware/auth');
const {
  generateVerificationCode,
  sendVerificationCode,
  sendWelcomeEmail
} = require('../utils/email');

const router = express.Router();

// Validation middleware
const validateRegistration = [
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email address'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage(
      'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    ),
  body('confirmPassword').custom((value, { req }) => {
    if (value !== req.body.password) {
      throw new Error('Password confirmation does not match password');
    }
    return true;
  })
];

const validateLogin = [
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email address'),
  body('password').notEmpty().withMessage('Password is required')
];

const validateVerificationCode = [
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email address'),
  body('code')
    .isLength({ min: 6, max: 6 })
    .isNumeric()
    .withMessage('Verification code must be 6 digits')
];

// Store verification codes temporarily (in production, use Redis)
const verificationCodes = new Map();

// Register user
router.post('/register', validateRegistration, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { email, password } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      return res.status(409).json({
        error: 'User already exists',
        message: 'An account with this email address already exists'
      });
    }

    // Create new user
    const user = await User.create({
      email,
      password,
      language: req.body.language || 'zh'
    });

    // Generate and send verification code
    const code = generateVerificationCode();
    const expiresAt = Date.now() + 10 * 60 * 1000; // 10 minutes

    verificationCodes.set(email, {
      code,
      expiresAt,
      attempts: 0
    });

    // Send verification code via email
    // Check EMAIL_TEST_MODE to determine if we should send real emails
    if (process.env.EMAIL_TEST_MODE === 'skip' || process.env.NODE_ENV === 'production') {
      await sendVerificationCode(email, code, user.language);
    } else {
      // In development mode with test mode enabled, just log the code
      console.log(`📧 Development mode: Verification code for ${email}: ${code}`);
    }

    res.status(201).json({
      message: 'User registered successfully. Please check your email for verification code.',
      userId: user.id,
      email: user.email
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      error: 'Registration failed',
      message: 'An error occurred during registration'
    });
  }
});

// Verify email with code
router.post('/verify-email', validateVerificationCode, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { email, code } = req.body;

    // Check verification code
    const storedData = verificationCodes.get(email);
    if (!storedData) {
      return res.status(400).json({
        error: 'Invalid verification code',
        message: 'No verification code found for this email'
      });
    }

    if (Date.now() > storedData.expiresAt) {
      verificationCodes.delete(email);
      return res.status(400).json({
        error: 'Verification code expired',
        message: 'Please request a new verification code'
      });
    }

    if (storedData.attempts >= 3) {
      verificationCodes.delete(email);
      return res.status(429).json({
        error: 'Too many attempts',
        message: 'Please request a new verification code'
      });
    }

    if (storedData.code !== code) {
      storedData.attempts += 1;
      return res.status(400).json({
        error: 'Invalid verification code',
        message: 'The verification code is incorrect'
      });
    }

    // Update user as verified
    const [updatedCount] = await User.update(
      {
        isEmailVerified: true,
        emailVerificationToken: null,
        emailVerificationExpires: null
      },
      { where: { email } }
    );

    if (updatedCount === 0) {
      return res.status(404).json({
        error: 'User not found',
        message: 'No user found with this email address'
      });
    }

    const user = await User.findOne({ where: { email } });

    // Clean up verification code
    verificationCodes.delete(email);

    // Send welcome email
    await sendWelcomeEmail(email, user.firstName, user.language);

    // Generate JWT token
    const token = generateToken(user.id);

    res.json({
      message: 'Email verified successfully',
      token,
      user: {
        id: user.id,
        email: user.email,
        isEmailVerified: user.isEmailVerified,
        language: user.language,
        subscriptionPlan: user.subscriptionPlan
      }
    });
  } catch (error) {
    console.error('Email verification error:', error);
    res.status(500).json({
      error: 'Verification failed',
      message: 'An error occurred during email verification'
    });
  }
});

// Resend verification code
router.post(
  '/resend-verification',
  [body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email address')],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { email } = req.body;

      // Check if user exists and is not verified
      const user = await User.findOne({ where: { email } });
      if (!user) {
        return res.status(404).json({
          error: 'User not found',
          message: 'No user found with this email address'
        });
      }

      if (user.isEmailVerified) {
        return res.status(400).json({
          error: 'Email already verified',
          message: 'This email address is already verified'
        });
      }

      // Generate and send new verification code
      const code = generateVerificationCode();
      const expiresAt = Date.now() + 10 * 60 * 1000; // 10 minutes

      verificationCodes.set(email, {
        code,
        expiresAt,
        attempts: 0
      });

      await sendVerificationCode(email, code, user.language);

      res.json({
        message: 'Verification code sent successfully'
      });
    } catch (error) {
      console.error('Resend verification error:', error);
      res.status(500).json({
        error: 'Failed to resend verification code',
        message: 'An error occurred while sending verification code'
      });
    }
  }
);

// Login user
router.post('/login', validateLogin, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { email, password } = req.body;

    // Find user
    const user = await User.findOne({ where: { email } });
    if (!user) {
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Email or password is incorrect'
      });
    }

    // Check if account is active
    if (!user.isActive) {
      return res.status(401).json({
        error: 'Account deactivated',
        message: 'Your account has been deactivated. Please contact support.'
      });
    }

    // Compare password using direct bcrypt.compare (more reliable)
    const isPasswordValid = await bcrypt.compare(password, user.password);

    if (!isPasswordValid) {
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Email or password is incorrect'
      });
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Generate JWT token
    const token = generateToken(user.id);

    res.json({
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        email: user.email,
        isEmailVerified: user.isEmailVerified,
        firstName: user.firstName,
        lastName: user.lastName,
        organization: user.organization,
        language: user.language,
        subscriptionPlan: user.subscriptionPlan,
        role: user.role,
        lastLogin: user.lastLogin
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      error: 'Login failed',
      message: 'An error occurred during login'
    });
  }
});

// Get current user
router.get('/me', authenticate, async (req, res) => {
  try {
    res.json({
      user: {
        id: req.user.id,
        email: req.user.email,
        role: req.user.role,
        isActive: req.user.isActive,
        isEmailVerified: req.user.isEmailVerified,
        firstName: req.user.firstName,
        lastName: req.user.lastName,
        organization: req.user.organization,
        phone: req.user.phone,
        country: req.user.country,
        language: req.user.language,
        emailNotifications: req.user.emailNotifications,
        analysisNotifications: req.user.analysisNotifications,
        subscriptionPlan: req.user.subscriptionPlan,
        lastLogin: req.user.lastLogin,
        createdAt: req.user.createdAt
      }
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      error: 'Failed to get user information',
      message: 'An error occurred while retrieving user information'
    });
  }
});

// Logout (client-side token removal, but we can track it)
router.post('/logout', authenticate, async (req, res) => {
  try {
    // In a more sophisticated setup, you might want to blacklist the token
    // For now, we'll just send a success response
    res.json({
      message: 'Logout successful'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      error: 'Logout failed',
      message: 'An error occurred during logout'
    });
  }
});

module.exports = router;
