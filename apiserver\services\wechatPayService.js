const WxPay = require('wechatpay-node-v3');
const QRCode = require('qrcode');

class WeChatPayService {
  constructor() {
    // 微信支付配置（这些应该从环境变量中获取）
    this.config = {
      appid: process.env.WECHAT_APPID || 'your_wechat_appid',
      mchid: process.env.WECHAT_MCHID || 'your_merchant_id',
      private_key: process.env.WECHAT_PRIVATE_KEY || '', // 商户私钥
      serial_no: process.env.WECHAT_SERIAL_NO || '', // 商户证书序列号
      apiv3_private_key: process.env.WECHAT_APIV3_KEY || '', // APIv3密钥
      notify_url:
        process.env.WECHAT_NOTIFY_URL ||
        'http://localhost:3001/api/payments/callback/wechat'
    };

    // 初始化微信支付实例（仅在有配置时）
    this.wxpay = null;
    if (this.config.private_key && this.config.appid && this.config.mchid) {
      try {
        this.wxpay = new WxPay({
          appid: this.config.appid,
          mchid: this.config.mchid,
          publicKey: '', // 平台证书（可选）
          privateKey: this.config.private_key
        });
      } catch (error) {
        console.warn('微信支付初始化失败，将使用模拟模式:', error.message);
      }
    } else {
      console.warn('微信支付配置不完整，将使用模拟模式');
    }
  }

  /**
   * 创建扫码支付订单
   * @param {Object} orderData - 订单数据
   * @returns {Promise<Object>} 支付订单信息
   */
  async createNativeOrder(orderData) {
    try {
      const { paymentId, amount, description } = orderData;

      // 如果没有配置微信支付，使用模拟模式
      if (!this.wxpay) {
        console.log('使用微信支付模拟模式');
        return {
          success: true,
          qrCodeContent: `weixin://wxpay/bizpayurl?pr=${paymentId}&amount=${amount}`,
          orderData: {
            code_url: `weixin://wxpay/bizpayurl?pr=${paymentId}&amount=${amount}`,
            prepay_id: `MOCK_PREPAY_${paymentId}`,
            out_trade_no: paymentId
          }
        };
      }

      const params = {
        appid: this.config.appid,
        mchid: this.config.mchid,
        description: description || '蛋白质分析报告',
        out_trade_no: paymentId, // 商户订单号
        notify_url: this.config.notify_url,
        amount: {
          total: Math.round(amount * 100), // 微信支付金额单位为分
          currency: 'CNY'
        },
        scene_info: {
          payer_client_ip: '127.0.0.1', // 用户终端IP
          store_info: {
            id: 'protein_analysis_platform',
            name: '蛋白质分析平台',
            area_code: '440105',
            address: '广东省广州市海珠区'
          }
        }
      };

      // 调用微信支付API创建订单
      const result = await this.wxpay.transactions_native(params);

      if (result.code_url) {
        return {
          success: true,
          qrCodeContent: result.code_url,
          orderData: result
        };
      } else {
        throw new Error('Failed to create WeChat Pay order');
      }
    } catch (error) {
      console.error('WeChat Pay create order error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 生成支付二维码图片
   * @param {string} codeUrl - 微信支付二维码链接
   * @returns {Promise<string>} Base64编码的二维码图片
   */
  async generateQRCode(codeUrl) {
    try {
      const qrCodeDataURL = await QRCode.toDataURL(codeUrl, {
        width: 256,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
      return qrCodeDataURL;
    } catch (error) {
      console.error('Generate QR code error:', error);
      throw error;
    }
  }

  /**
   * 查询订单状态
   * @param {string} outTradeNo - 商户订单号
   * @returns {Promise<Object>} 订单状态信息
   */
  async queryOrder(outTradeNo) {
    try {
      const result = await this.wxpay.query({
        out_trade_no: outTradeNo
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('WeChat Pay query order error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 关闭订单
   * @param {string} outTradeNo - 商户订单号
   * @returns {Promise<Object>} 关闭结果
   */
  async closeOrder(outTradeNo) {
    try {
      const result = await this.wxpay.close({
        out_trade_no: outTradeNo
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('WeChat Pay close order error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 验证支付回调签名
   * @param {Object} headers - 请求头
   * @param {string} body - 请求体
   * @returns {boolean} 验证结果
   */
  verifySignature(_headers, _body) {
    try {
      // 这里应该实现微信支付的签名验证逻辑
      // 由于涉及到证书和密钥，这里简化处理

      // 实际项目中需要使用微信提供的验签方法
      // return this.wxpay.verifySign({ timestamp, nonce, body, signature, serial });

      // 开发环境下暂时返回true
      return process.env.NODE_ENV !== 'production';
    } catch (error) {
      console.error('WeChat Pay verify signature error:', error);
      return false;
    }
  }

  /**
   * 解析支付回调数据
   * @param {string} encryptedData - 加密的回调数据
   * @returns {Object} 解析后的数据
   */
  decryptCallbackData(encryptedData) {
    try {
      // 这里应该实现微信支付回调数据的解密逻辑
      // 由于涉及到APIv3密钥，这里简化处理

      // 实际项目中需要使用微信提供的解密方法
      // return this.wxpay.decrypt(encryptedData);

      // 开发环境下直接返回原数据
      return JSON.parse(encryptedData);
    } catch (error) {
      console.error('WeChat Pay decrypt callback data error:', error);
      throw error;
    }
  }

  /**
   * 创建退款订单
   * @param {Object} refundData - 退款数据
   * @returns {Promise<Object>} 退款结果
   */
  async createRefund(refundData) {
    try {
      const { outTradeNo, refundAmount, totalAmount, reason } = refundData;

      const params = {
        out_trade_no: outTradeNo,
        out_refund_no: `REFUND_${outTradeNo}_${Date.now()}`,
        reason: reason || '用户申请退款',
        amount: {
          refund: Math.round(refundAmount * 100), // 退款金额（分）
          total: Math.round(totalAmount * 100), // 原订单金额（分）
          currency: 'CNY'
        }
      };

      const result = await this.wxpay.refund(params);

      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('WeChat Pay create refund error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = WeChatPayService;
