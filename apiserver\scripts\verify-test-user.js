const { User } = require('../models');

async function verifyTestUser() {
  try {
    console.log('🔧 验证测试用户邮箱...');

    // 查找测试用户
    const testUser = await User.findOne({
      where: { email: '<EMAIL>' }
    });

    if (!testUser) {
      console.log('❌ 未找到测试用户');
      return false;
    }

    console.log(`👤 找到用户: ${testUser.email}`);
    console.log(`🔍 当前邮箱验证状态: ${testUser.isEmailVerified}`);
    console.log(`🔍 当前账户状态: ${testUser.isActive}`);

    if (testUser.isEmailVerified && testUser.isActive) {
      console.log('✅ 用户已经验证并激活');
      return true;
    }

    // 验证邮箱并激活账户
    testUser.isEmailVerified = true;
    testUser.isActive = true;
    testUser.emailVerifiedAt = new Date();
    await testUser.save();

    console.log('✅ 用户邮箱验证和激活成功！');
    console.log(`📧 邮箱验证: ${testUser.isEmailVerified}`);
    console.log(`✅ 账户激活: ${testUser.isActive}`);
    console.log(`📅 验证时间: ${testUser.emailVerifiedAt}`);

    return true;

  } catch (error) {
    console.error('❌ 验证失败:', error.message);
    return false;
  }
}

// 运行验证
verifyTestUser().then(success => {
  if (success) {
    console.log('');
    console.log('🎯 测试用户验证完成！');
    console.log('🌐 现在用户可以正常创建申请和使用所有功能');
  } else {
    console.log('');
    console.log('🔧 验证失败，请检查错误信息');
  }

  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ 脚本执行失败:', error.message);
  process.exit(1);
});
