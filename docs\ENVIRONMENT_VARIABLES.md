# Environment Variables Configuration

## Cross-Platform Compatibility

This document explains how Quantix handles environment variables to ensure consistent behavior across Windows, Linux, and macOS platforms.

## Problem Statement

Docker Compose handles boolean and numeric environment variables differently across platforms:

- **Windows**: `USE_MYSQL: true` (boolean) works correctly
- **Linux/Ubuntu**: `USE_MYSQL: "true"` (string) is required
- **macOS**: Similar to Linux, requires string format

## Solution

We've implemented a unified environment variable parsing system that handles both formats consistently.

### Environment Variable Parser

The `apiserver/utils/env.js` module provides cross-platform environment variable parsing:

```javascript
const { getEnvConfig } = require('./utils/env');
const env = getEnvConfig();

// Now use env.USE_MYSQL instead of process.env.USE_MYSQL
```

### Supported Data Types

#### Boolean Variables
Supports multiple formats (case-insensitive):
- `true`, `"true"` → `true`
- `false`, `"false"` → `false`
- `1`, `"1"` → `true`
- `0`, `"0"` → `false`
- `yes`, `"yes"` → `true`
- `no`, `"no"` → `false`
- `on`, `"on"` → `true`
- `off`, `"off"` → `false`

#### Numeric Variables
Automatically converts strings to numbers:
- `PORT: "3001"` → `3001` (number)
- `DB_PORT: 3306` → `3306` (number)

#### File Size Variables
Supports human-readable formats:
- `MAX_FILE_SIZE: "100MB"` → `104857600` (bytes)
- `MAX_FILE_SIZE: "1GB"` → `1073741824` (bytes)
- `MAX_FILE_SIZE: 1048576` → `1048576` (bytes)

## Docker Compose Configuration

### Recommended Format (Cross-Platform)

```yaml
environment:
  NODE_ENV: production
  PORT: "3001"                    # String format
  USE_MYSQL: "true"              # String format for boolean
  DB_HOST: mysql
  DB_PORT: "3306"                # String format for number
  DB_NAME: quantix
  DB_USER: quantix_user
  DB_PASSWORD: quantix_password_2024
  MAX_FILE_SIZE: "104857600"     # String format for file size
```

### Legacy Format (Windows Only)

```yaml
environment:
  NODE_ENV: production
  PORT: 3001                     # Numeric format
  USE_MYSQL: true               # Boolean format
  DB_PORT: 3306                 # Numeric format
  MAX_FILE_SIZE: 104857600      # Numeric format
```

## Environment Variables Reference

### Server Configuration
- `NODE_ENV`: Environment mode (`development`, `production`, `test`)
- `PORT`: Server port (default: `3001`)
- `FRONTEND_URL`: Frontend URL for CORS (default: `http://localhost:5173`)

### Database Configuration
- `USE_MYSQL`: Use MySQL instead of SQLite (default: `false`)
- `DB_HOST`: MySQL host (default: `localhost`)
- `DB_PORT`: MySQL port (default: `3306`)
- `DB_NAME`: Database name (default: `quantix`)
- `DB_USER`: Database user (default: `root`)
- `DB_PASSWORD`: Database password (default: empty)

### Security Configuration
- `JWT_SECRET`: JWT signing secret (required in production)
- `JWT_EXPIRE`: JWT expiration time (default: `7d`)

### Email Configuration
- `EMAIL_SERVICE`: Email service provider (`gmail`, `qq`, etc.)
- `EMAIL_USER`: Email username
- `EMAIL_PASS`: Email password or app password
- `EMAIL_TEST_MODE`: Skip email sending in development (`skip`)

### File Upload Configuration
- `MAX_FILE_SIZE`: Maximum file size (default: `100MB`)
- `UPLOAD_PATH`: Upload directory path (default: `./uploads`)

### AI Service Configuration
- `OPENAI_API_KEY`: OpenAI API key
- `OPENAI_MODEL`: OpenAI model (default: `gpt-3.5-turbo`)

### Payment Configuration
- `STRIPE_SECRET_KEY`: Stripe secret key
- `STRIPE_PUBLISHABLE_KEY`: Stripe publishable key

## Migration Guide

### For Existing Deployments

1. **Update docker-compose.yml**: Change all boolean and numeric values to strings
2. **No code changes required**: The environment parser handles both formats
3. **Test deployment**: Verify that all environment variables are parsed correctly

### Example Migration

**Before:**
```yaml
environment:
  USE_MYSQL: true
  PORT: 3001
  DB_PORT: 3306
```

**After:**
```yaml
environment:
  USE_MYSQL: "true"
  PORT: "3001"
  DB_PORT: "3306"
```

## Best Practices

1. **Always use string format** in Docker Compose files for cross-platform compatibility
2. **Use the environment parser** in application code instead of direct `process.env` access
3. **Provide sensible defaults** for all environment variables
4. **Document required variables** in `.env.example` files
5. **Validate critical variables** at application startup

## Troubleshooting

### Common Issues

1. **Boolean not recognized**: Ensure the value is `"true"` or `"false"` (string format)
2. **Port binding fails**: Check that numeric values are properly converted
3. **File size limits**: Use human-readable format like `"100MB"` instead of raw bytes

### Debug Environment Variables

Add this to your application startup to debug environment parsing:

```javascript
const { getEnvConfig } = require('./utils/env');
const env = getEnvConfig();
console.log('Parsed environment:', env);
```

## Testing

Test environment variable parsing across platforms:

```bash
# Test boolean parsing
USE_MYSQL="true" npm test
USE_MYSQL=true npm test

# Test numeric parsing
PORT="3001" npm test
PORT=3001 npm test

# Test file size parsing
MAX_FILE_SIZE="100MB" npm test
MAX_FILE_SIZE=104857600 npm test
```
