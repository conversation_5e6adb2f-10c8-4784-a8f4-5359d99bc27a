# Docker Compose 开发环境配置
# 用于覆盖生产环境配置，提供开发友好的设置

version: '3.8'

services:
  # 开发环境数据库配置
  mysql:
    ports:
      - '3306:3306' # 暴露数据库端口用于开发调试
    environment:
      - MYSQL_ROOT_PASSWORD=dev_root_password
      - MYSQL_DATABASE=quantix_dev
      - MYSQL_USER=quantix_dev
      - MYSQL_PASSWORD=dev_password
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ../apiserver/scripts/mysql-init.sql:/docker-entrypoint-initdb.d/init.sql:ro

  # 开发环境后端配置
  backend:
    environment:
      - NODE_ENV=development
      - DATABASE_URL=mysql://quantix_dev:dev_password@mysql:3306/quantix_dev
      - JWT_SECRET=dev_jwt_secret_key_for_development_only
      - FRONTEND_URL=http://localhost:5173
      - DEBUG=quantix:*
    volumes:
      - ../apiserver:/app # 挂载源代码用于热重载
      - /app/node_modules # 保护node_modules
    command: npm run dev # 使用开发模式启动
    depends_on:
      - mysql

  # 开发环境前端配置
  frontend:
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:3001
    volumes:
      - ../web/src:/app/src # 挂载源代码用于热重载
      - ../web/public:/app/public
      - /app/node_modules # 保护node_modules
    command: npm run dev -- --host 0.0.0.0 # 允许外部访问
    stdin_open: true # 支持交互式输入
    tty: true

  # 开发环境Nginx配置（可选）
  nginx:
    image: nginx:alpine
    ports:
      - '8080:80' # 使用不同端口避免冲突
    volumes:
      - ./nginx.dev.conf:/etc/nginx/nginx.conf:ro # 使用开发环境nginx配置
    depends_on:
      - frontend
      - backend

volumes:
  mysql_dev_data:
    driver: local

networks:
  default:
    name: quantix_dev_network
