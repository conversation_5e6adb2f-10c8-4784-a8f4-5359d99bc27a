# ============================================================================
# Quantix Protein Analysis Platform - .gitignore
# ============================================================================

# ============================================================================
# Dependencies and Package Managers
# ============================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Lock files (uncomment the ones you want to ignore)
# Note: It's generally recommended to commit lock files for consistency
# package-lock.json
yarn.lock
pnpm-lock.yaml

# ============================================================================
# Build Outputs and Distribution
# ============================================================================
dist/
dist-ssr/
build/
out/
*.tsbuildinfo

# ============================================================================
# Environment Variables and Configuration
# ============================================================================
# Environment files (keep .env.example for reference)
.env
.env.local
.env.development
.env.test
.env.production
.env.*.local

# Server environment files
server/.env
server/.env.local
server/.env.development
server/.env.test
server/.env.production

# ============================================================================
# Database Files
# ============================================================================
# SQLite databases
*.sqlite
*.sqlite3
*.db
server/database.sqlite
server/*.db

# MySQL dumps
*.sql
*.dump

# Database migration files (if auto-generated)
server/migrations/auto-generated-*

# ============================================================================
# File Uploads and User Content
# ============================================================================
# User uploaded files
server/uploads/
uploads/
files/
temp/
tmp/

# Protein analysis files
*.pdb
*.fasta
*.mol
*.xyz
*.cif

# Report files
reports/
*.pdf
*.doc
*.docx

# ============================================================================
# Logs and Runtime Files
# ============================================================================
logs/
*.log
*.log.*
server/logs/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# ============================================================================
# Testing and Coverage
# ============================================================================
coverage/
*.lcov
.nyc_output/

# Playwright
test-results/
playwright-report/
playwright/.cache/

# Cypress
/cypress/videos/
/cypress/screenshots/

# Jest
jest-coverage/

# Vitest
vitest-coverage/

# ============================================================================
# Development Tools and Cache
# ============================================================================
# Vite
.vite/
vite.config.js.timestamp-*
vite.config.ts.timestamp-*

# TypeScript
*.tsbuildinfo

# ESLint
.eslintcache

# Prettier
.prettierignore

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/

# ============================================================================
# Operating System Files
# ============================================================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.Spotlight-V100
.Trashes

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ============================================================================
# Editor and IDE Files
# ============================================================================
# VSCode
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-workspace
*.sublime-project

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Other editors
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# ============================================================================
# Docker and Container Files
# ============================================================================
# Docker volumes and data
docker-data/
docker/data/
docker/mysql-data/
docker/logs/

# Docker override files
docker-compose.override.yml
docker-compose.local.yml

# ============================================================================
# Security and Sensitive Files
# ============================================================================
# API keys and secrets
.secrets
secrets/
*.key
*.pem
*.p12
*.pfx

# SSL certificates
*.crt
*.cer
*.der

# ============================================================================
# Temporary and Development Files
# ============================================================================
# Temporary directories
tmp/
temp/
.tmp/
.temp/

# Development scripts and test files
dev-scripts/
test-scripts/
debug-*
*.test.local.*

# Backup files
*.bak
*.backup
*.old
*.orig

# ============================================================================
# Package Manager and Runtime
# ============================================================================
# npm
.npm
.npmrc

# Yarn
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# ============================================================================
# Monitoring and Analytics
# ============================================================================
# Error tracking
.sentry/

# Analytics
analytics/
metrics/

# ============================================================================
# Documentation Build
# ============================================================================
# Generated documentation
docs/build/
docs/dist/
docs/.vuepress/dist/

# ============================================================================
# Miscellaneous
# ============================================================================
# Local configuration overrides
*.local
local.*

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Large media files (unless specifically needed)
*.mp4
*.avi
*.mov
*.wmv
*.flv

# ============================================================================
# Project Specific Exclusions and Exceptions
# ============================================================================
# Keep important configuration examples
!.env.example
!server/.env.example
!docker/.env.example

# Keep important project files
!package.json
!server/package.json
!tsconfig*.json
!vite.config.*
!vitest.config.*
!playwright.config.*
!eslint.config.*

# Keep important documentation
!README.md
!docs/
!docs/**/*.md

# Keep source code
!src/
!server/index.js
!server/routes/
!server/models/
!server/middleware/
!server/services/
!server/utils/
!server/config/

# Keep Docker configuration
!docker/
!docker/Dockerfile*
!docker/docker-compose*.yml
!docker/nginx*.conf

# Keep scripts
!scripts/
!scripts/*.sh
!scripts/*.bat
!scripts/*.ps1

# Keep VSCode extensions configuration
!.vscode/extensions.json
!.vscode/settings.json.example

# Keep important root files
!.gitignore
!index.html
!env.d.ts
