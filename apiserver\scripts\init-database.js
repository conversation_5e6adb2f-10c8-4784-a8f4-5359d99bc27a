const { sequelize, User } = require('../models');
require('dotenv').config();

async function initDatabase() {
  try {
    console.log('🔄 Connecting to database...');
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    console.log('🔄 Synchronizing database schema...');
    await sequelize.sync({ force: false, alter: true });
    console.log('✅ Database schema synchronized');

    // Create default admin user
    console.log('🔄 Creating default admin user...');
    const adminExists = await User.findOne({ where: { email: '<EMAIL>' } });

    if (!adminExists) {
      const _adminUser = await User.create({
        email: '<EMAIL>',
        password: 'fHadmin',
        isEmailVerified: true,
        firstName: 'System',
        lastName: 'Administrator',
        organization: 'Quantix Platform',
        role: 'admin',
        subscriptionPlan: 'premium',
        language: 'zh'
      });
      console.log('✅ Default admin user created successfully');
      console.log('📧 Admin email: <EMAIL>');
      console.log('🔑 Admin password: fHadmin');
    } else {
      console.log('ℹ️  Admin user already exists');
    }

    console.log('🎉 Database initialization completed successfully!');

  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);
    throw error; // Throw error instead of exiting process
  }
}

if (require.main === module) {
  initDatabase();
}

module.exports = initDatabase;
