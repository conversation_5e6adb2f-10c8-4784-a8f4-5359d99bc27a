import { createRouter, createWebHistory } from "vue-router";
import type { RouteLocationNormalized, NavigationGuardNext } from "vue-router";
import { useUserStore } from "@/stores/counter";

// Extend route meta interface
declare module "vue-router" {
  interface RouteMeta {
    requiresAuth?: boolean;
  }
}
import HomeLandingView from "../views/HomeLandingView.vue";
import UnifiedLoginView from "../views/UnifiedLoginView.vue";
import RegisterView from "../views/RegisterView.vue";
import CaptchaInputView from "../views/CaptchaInputView.vue";
import ChatView from "../views/ChatView.vue";
import SystemInitView from "../views/SystemInitView.vue";
import DebugMockApiView from "../views/DebugMockApiView.vue";
import MockApiTestView from "../views/MockApiTestView.vue";

// Console (User) Views
import ConsoleDashboardView from "../views/console/ConsoleDashboardView.vue";
import ConsoleReportView from "../views/console/ConsoleReportView.vue";
import ConsolePaymentView from "../views/console/ConsolePaymentView.vue";
import ConsolePaymentSuccessView from "../views/console/ConsolePaymentSuccessView.vue";

// Admin Views
import AdminDashboardView from "../views/admin/AdminDashboardView.vue";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // Public routes
    {
      path: "/",
      name: "home-landing",
      component: HomeLandingView,
    },
    {
      path: "/login",
      name: "login",
      component: UnifiedLoginView,
    },
    {
      path: "/register",
      name: "register",
      component: RegisterView,
    },
    {
      path: "/captcha-input",
      name: "captcha-input",
      component: CaptchaInputView,
    },
    {
      path: "/chat",
      name: "chat",
      component: ChatView,
    },
    {
      path: "/system/init",
      name: "system-init",
      component: SystemInitView,
    },
    {
      path: "/debug/mock-api",
      name: "debug-mock-api",
      component: DebugMockApiView,
    },
    {
      path: "/test/mock-api",
      name: "test-mock-api",
      component: MockApiTestView,
    },

    // Console (User) routes
    {
      path: "/console",
      name: "console",
      component: ConsoleDashboardView,
      meta: { requiresAuth: true },
    },
    {
      path: "/console/report/:id",
      name: "console-report",
      component: ConsoleReportView,
      meta: { requiresAuth: true },
    },
    {
      path: "/console/payment",
      name: "console-payment",
      component: ConsolePaymentView,
      meta: { requiresAuth: true },
    },
    {
      path: "/console/payment/success",
      name: "console-payment-success",
      component: ConsolePaymentSuccessView,
      meta: { requiresAuth: true },
    },

    // Admin routes
    {
      path: "/admin",
      name: "admin",
      component: AdminDashboardView,
      meta: { requiresAuth: true },
    },

    // Legacy redirects for backward compatibility
    {
      path: "/dashboard",
      redirect: "/console",
    },
    {
      path: "/console/dashboard",
      redirect: "/console",
    },
    {
      path: "/report/:id",
      redirect: (to) => `/console/report/${to.params.id}`,
    },
    {
      path: "/payment",
      redirect: "/console/payment",
    },
    {
      path: "/payment/success",
      redirect: "/console/payment/success",
    },
    {
      path: "/admin/login",
      redirect: "/login",
    },
  ],
});

// Route guards
router.beforeEach(async (to: RouteLocationNormalized, _from: RouteLocationNormalized, next: NavigationGuardNext) => {
  const userStore = useUserStore();

  // Skip system check for system init route and static assets
  if (to.name === 'system-init' || to.path.startsWith('/assets/')) {
    next();
    return;
  }

  // Check system initialization status for all routes except system-init
  try {
    const { checkSystemInitialization } = await import('@/utils/systemCheck');
    const systemStatus = await checkSystemInitialization();

    if (!systemStatus.isInitialized) {
      // System not initialized, redirect to init page
      next({ name: 'system-init' });
      return;
    }
  } catch (error) {
    console.error('Failed to check system status:', error);
    // If check fails, redirect to init page to be safe
    next({ name: 'system-init' });
    return;
  }

  // Check if route requires authentication
  if (to.meta.requiresAuth) {
    if (!userStore.isAuthenticated) {
      // Redirect to login if not authenticated
      next({ name: "login", query: { redirect: to.fullPath } });
      return;
    }

    // No role-based restrictions - let users choose their interface
    // Both admin and user interfaces are accessible to all authenticated users
  }

  next();
});

export default router;
