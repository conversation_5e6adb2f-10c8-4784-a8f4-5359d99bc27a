const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const { getEnvConfig } = require('../utils/env');

// Get parsed environment configuration
const env = getEnvConfig();

// Ensure upload directory exists
const uploadDir = env.UPLOAD_PATH;
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Configure storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // Create user-specific directory
    const userDir = path.join(uploadDir, req.user.id.toString());
    if (!fs.existsSync(userDir)) {
      fs.mkdirSync(userDir, { recursive: true });
    }
    cb(null, userDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueId = uuidv4();
    const ext = path.extname(file.originalname);
    const filename = `${uniqueId}${ext}`;
    cb(null, filename);
  }
});

// File filter for genetic data files
const fileFilter = (req, file, cb) => {
  const allowedTypes = [
    'text/plain',
    'application/octet-stream',
    'text/x-fasta',
    'application/x-fasta',
    'text/x-fastq',
    'application/x-fastq',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ];

  const allowedExtensions = [
    '.fasta',
    '.fa',
    '.fas',
    '.fna',
    '.ffn',
    '.faa',
    '.frn',
    '.fastq',
    '.fq',
    '.pdb',
    '.ent',
    '.sdf',
    '.mol',
    '.txt',
    '.seq',
    '.pdf',
    '.doc',
    '.docx'
  ];

  const ext = path.extname(file.originalname).toLowerCase();

  if (allowedTypes.includes(file.mimetype) || allowedExtensions.includes(ext)) {
    cb(null, true);
  } else {
    cb(new Error(`Unsupported file type. Allowed types: ${allowedExtensions.join(', ')}`), false);
  }
};

// Configure multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: env.MAX_FILE_SIZE, // Parsed with proper type conversion
    files: 5 // Maximum 5 files per upload
  }
});

// Middleware to handle upload errors
const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        error: 'File too large',
        message: `File size exceeds the maximum limit of ${Math.round(env.MAX_FILE_SIZE / (1024 * 1024))}MB`
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        error: 'Too many files',
        message: 'Maximum 5 files allowed per upload'
      });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        error: 'Unexpected field',
        message: 'Unexpected file field in upload'
      });
    }
  }

  if (error.message.includes('Unsupported file type')) {
    return res.status(400).json({
      error: 'Unsupported file type',
      message: error.message
    });
  }

  next(error);
};

// File validation functions
const validateFastaFile = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');

    let hasHeader = false;
    let hasSequence = false;

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine.startsWith('>')) {
        hasHeader = true;
      } else if (trimmedLine.length > 0 && /^[ACGTUNRYSWKMBDHV-]+$/i.test(trimmedLine)) {
        hasSequence = true;
      }
    }

    return hasHeader && hasSequence;
  } catch {
    return false;
  }
};

const validateFastqFile = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n').filter((line) => line.trim());

    // FASTQ files should have lines in groups of 4
    if (lines.length % 4 !== 0) return false;

    for (let i = 0; i < lines.length; i += 4) {
      // Check header line starts with @
      if (!lines[i].startsWith('@')) return false;

      // Check sequence line contains valid nucleotides
      if (!/^[ACGTUNRYSWKMBDHV-]+$/i.test(lines[i + 1])) return false;

      // Check separator line starts with +
      if (!lines[i + 2].startsWith('+')) return false;

      // Check quality line length matches sequence length
      if (lines[i + 3].length !== lines[i + 1].length) return false;
    }

    return true;
  } catch {
    return false;
  }
};

const validatePdbFile = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');

    let hasAtom = false;
    let _hasHeader = false;

    for (const line of lines) {
      if (line.startsWith('HEADER')) {
        _hasHeader = true;
      } else if (line.startsWith('ATOM') || line.startsWith('HETATM')) {
        hasAtom = true;
      }
    }

    return hasAtom; // Header is optional, but atoms are required
  } catch {
    return false;
  }
};

// Determine file type based on content and extension
const determineFileType = (filePath, originalName) => {
  const ext = path.extname(originalName).toLowerCase();

  if (['.fasta', '.fa', '.fas', '.fna', '.ffn', '.faa', '.frn'].includes(ext)) {
    return validateFastaFile(filePath) ? 'fasta' : 'other';
  }

  if (['.fastq', '.fq'].includes(ext)) {
    return validateFastqFile(filePath) ? 'fastq' : 'other';
  }

  if (['.pdb', '.ent'].includes(ext)) {
    return validatePdbFile(filePath) ? 'pdb' : 'other';
  }

  if (['.sdf', '.mol'].includes(ext)) {
    return 'sdf';
  }

  // Try to auto-detect based on content
  if (validateFastaFile(filePath)) return 'fasta';
  if (validateFastqFile(filePath)) return 'fastq';
  if (validatePdbFile(filePath)) return 'pdb';

  return 'other';
};

// Get file statistics
const getFileStats = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const fileSize = fs.statSync(filePath).size;

    return {
      size: fileSize,
      lineCount: lines.length,
      characterCount: content.length,
      isEmpty: content.trim().length === 0
    };
  } catch {
    return null;
  }
};

// Clean up old files (call this periodically)
const cleanupOldFiles = (maxAgeHours = 24) => {
  const maxAge = maxAgeHours * 60 * 60 * 1000; // Convert to milliseconds
  const now = Date.now();

  const cleanupDirectory = (dir) => {
    if (!fs.existsSync(dir)) return;

    const files = fs.readdirSync(dir);

    files.forEach((file) => {
      const filePath = path.join(dir, file);
      const stats = fs.statSync(filePath);

      if (stats.isDirectory()) {
        cleanupDirectory(filePath);
        // Remove empty directories
        try {
          fs.rmdirSync(filePath);
        } catch {
          // Directory not empty, ignore
        }
      } else if (stats.isFile()) {
        const age = now - stats.mtime.getTime();
        if (age > maxAge) {
          try {
            fs.unlinkSync(filePath);
            console.log(`Cleaned up old file: ${filePath}`);
          } catch (error) {
            console.error(`Failed to cleanup file ${filePath}:`, error);
          }
        }
      }
    });
  };

  cleanupDirectory(uploadDir);
};

module.exports = {
  upload,
  handleUploadError,
  determineFileType,
  getFileStats,
  cleanupOldFiles,
  validateFastaFile,
  validateFastqFile,
  validatePdbFile
};
