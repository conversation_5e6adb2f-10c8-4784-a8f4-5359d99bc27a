# Mock API 配置说明

## 概述

Quantix 项目现在支持 Mock API 功能，用于前端开发和调试。通过环境变量控制是否使用 Mock API，无需启动后端服务即可进行前端开发。

## 配置方法

### 1. 环境变量配置

在 `web/.env` 文件中设置：

```bash
# 启用 Mock API
VITE_USE_MOCK_API=true

# 禁用 Mock API（使用真实 API）
VITE_USE_MOCK_API=false
```

### 2. API 服务选择器

项目使用 `web/src/services/apiService.js` 作为 API 服务选择器：

- 根据 `VITE_USE_MOCK_API` 环境变量自动选择使用 Mock API 或真实 API
- 提供统一的 API 接口，组件无需修改代码
- 在浏览器控制台显示当前使用的 API 类型

### 3. 文件结构

```
web/src/services/
├── api.js              # 真实 API 实现
├── mockApi.js          # Mock API 实现
├── apiService.js       # API 服务选择器（新增）
├── api.d.ts            # 真实 API 类型声明
├── mockApi.d.ts        # Mock API 类型声明
└── apiService.d.ts     # API 服务选择器类型声明（新增）
```

## 使用方法

### 1. 在组件中使用

```javascript
// 旧方式（已更新）
import { authAPI, systemAPI } from '@/services/api'

// 新方式
import { authAPI, systemAPI } from '@/services/apiService'
```

### 2. 测试页面

项目提供了两个测试页面：

- **调试页面**: `http://localhost:5173/debug/mock-api`
  - 原有的 Mock API 调试工具
  - 提供各种 API 测试按钮

- **测试页面**: `http://localhost:5173/test/mock-api`
  - 新增的简化测试页面
  - 显示当前 API 状态
  - 提供常用 API 测试功能

### 3. 控制台输出

启动前端时，控制台会显示：

```
🔧 API Service Configuration: {
  USE_MOCK_API: true,
  VITE_USE_MOCK_API: "true",
  VITE_API_BASE_URL: "http://localhost:3000/api"
}
🎭 Using Mock API for development
```

## Mock API 功能

### 支持的 API

- **认证 API** (`authAPI`)
  - 登录、注册、邮箱验证
  - 获取当前用户信息

- **系统 API** (`systemAPI`)
  - 检查初始化状态
  - 系统初始化

- **应用 API** (`applicationAPI`)
  - 获取应用列表
  - 提交应用
  - 下载报告

- **上传 API** (`uploadAPI`)
  - 文件上传功能

- **管理员 API** (`adminAPI`)
  - 仪表板统计
  - 用户管理

- **支付 API** (`paymentAPI`)
  - 创建支付
  - 支付状态查询
  - 支付模拟

- **聊天 API** (`chatAPI`)
  - 发送消息
  - 获取建议

- **健康检查 API** (`healthAPI`)
  - 服务健康状态

### Mock 数据特点

- 返回真实的数据结构
- 模拟网络延迟（500-2000ms）
- 包含错误处理
- 支持分页和筛选
- 提供丰富的测试数据

## 开发流程

### 1. 启用 Mock API 开发

```bash
# 1. 设置环境变量
echo "VITE_USE_MOCK_API=true" > web/.env

# 2. 启动前端
cd web
npm run dev

# 3. 访问测试页面
# http://localhost:5173/test/mock-api
```

### 2. 切换到真实 API

```bash
# 1. 修改环境变量
echo "VITE_USE_MOCK_API=false" > web/.env

# 2. 启动后端服务
npm run dev

# 3. 前端会自动重新加载并使用真实 API
```

## 注意事项

1. **环境变量**: 修改 `.env` 文件后，Vite 会自动重启服务
2. **类型安全**: 所有 API 都有 TypeScript 类型声明
3. **错误处理**: Mock API 包含错误情况的模拟
4. **数据持久性**: Mock 数据在页面刷新后会重置
5. **网络模拟**: Mock API 会模拟网络延迟，更接近真实环境

## 故障排除

### 1. API 未切换

检查环境变量是否正确设置：
```javascript
console.log(import.meta.env.VITE_USE_MOCK_API)
```

### 2. 导入错误

确保所有组件都从 `@/services/apiService` 导入 API，而不是 `@/services/api`

### 3. 类型错误

确保安装了所有类型声明文件，并且 TypeScript 配置正确

## 扩展 Mock API

如需添加新的 Mock API：

1. 在 `mockApi.js` 中添加实现
2. 在 `apiService.js` 中添加选择逻辑
3. 更新类型声明文件
4. 在测试页面中添加测试用例
