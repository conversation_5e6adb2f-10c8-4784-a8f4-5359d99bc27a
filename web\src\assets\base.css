/* 全局样式 */
:root {
  --color-primary: #007bff;
  --color-secondary: #6c757d;
  --color-success: #28a745;
  --color-danger: #dc3545;
  --color-warning: #ffc107;
  --color-info: #17a2b8;
  --color-light: #f8f9fa;
  --color-dark: #343a40;

  --color-text-dark: #333;
  --color-text-medium: #555;
  --color-text-light: #777;

  --color-background-light: #f4f7f6;
  --color-background-white: #ffffff;

  --border-color: #ddd;
  --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.05);
  --shadow-medium: 0 10px 25px rgba(0, 0, 0, 0.1);
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Arial', sans-serif;
  line-height: 1.6;
  color: var(--color-text-dark);
  background-color: var(--color-background-light);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  color: var(--color-primary);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

button {
  font-family: 'Arial', sans-serif;
}
