/// <reference types="vite/client" />

// Environment variables type declarations
interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string
  readonly NODE_ENV: 'development' | 'production' | 'test'
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// Process environment for Node.js compatibility
declare namespace NodeJS {
  interface ProcessEnv {
    NODE_ENV: 'development' | 'production' | 'test'
  }
}

// Global process object for Node.js compatibility in browser
declare const process: {
  env: NodeJS.ProcessEnv
}
