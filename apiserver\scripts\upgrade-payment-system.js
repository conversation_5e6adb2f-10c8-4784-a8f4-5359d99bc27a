#!/usr/bin/env node

/**
 * 支付系统升级脚本
 * 
 * 此脚本用于将老版本的支付系统（独立的Payment表）升级到新版本（合并到Application表）
 * 
 * 使用方法：
 * node scripts/upgrade-payment-system.js [--dry-run] [--force]
 * 
 * 参数：
 * --dry-run: 只检查不执行，显示将要进行的操作
 * --force: 强制执行，即使检测到风险
 */

const { sequelize } = require('../models');
const { Sequelize } = require('sequelize');

class PaymentSystemUpgrader {
  constructor(options = {}) {
    this.dryRun = options.dryRun || false;
    this.force = options.force || false;
    this.backupCreated = false;
  }

  async run() {
    try {
      console.log('🚀 开始支付系统升级检查...\n');
      
      // 1. 检查数据库连接
      await this.checkDatabaseConnection();
      
      // 2. 检查当前系统状态
      const systemStatus = await this.checkSystemStatus();
      
      // 3. 根据状态决定操作
      if (systemStatus.needsUpgrade) {
        console.log('📋 系统需要升级，准备执行升级操作...\n');
        await this.performUpgrade(systemStatus);
      } else if (systemStatus.isUpgraded) {
        console.log('✅ 系统已经是最新版本，无需升级。\n');
        await this.verifyUpgradedSystem();
      } else {
        console.log('❓ 无法确定系统状态，请检查数据库结构。\n');
      }
      
      console.log('🎉 升级检查完成！');
      
    } catch (error) {
      console.error('❌ 升级过程中发生错误:', error.message);
      console.error('详细错误信息:', error);
      process.exit(1);
    } finally {
      await sequelize.close();
    }
  }

  async checkDatabaseConnection() {
    console.log('🔍 检查数据库连接...');
    try {
      await sequelize.authenticate();
      console.log('✅ 数据库连接正常\n');
    } catch (error) {
      throw new Error(`数据库连接失败: ${error.message}`);
    }
  }

  async checkSystemStatus() {
    console.log('🔍 检查系统状态...');
    
    const queryInterface = sequelize.getQueryInterface();
    const tables = await queryInterface.showAllTables();
    
    console.log('📊 当前数据库表:', tables.join(', '));
    
    const hasPaymentsTable = tables.includes('payments');
    const hasApplicationsTable = tables.includes('applications');
    
    if (!hasApplicationsTable) {
      throw new Error('未找到applications表，请确认数据库结构正确');
    }
    
    // 检查applications表是否有新的支付字段
    const applicationColumns = await queryInterface.describeTable('applications');
    const hasPaymentFields = 'paymentOrderId' in applicationColumns;
    
    let paymentsCount = 0;
    let applicationsCount = 0;
    
    if (hasPaymentsTable) {
      const [results] = await sequelize.query('SELECT COUNT(*) as count FROM payments');
      paymentsCount = results[0].count;
    }
    
    const [appResults] = await sequelize.query('SELECT COUNT(*) as count FROM applications');
    applicationsCount = appResults[0].count;
    
    console.log(`📈 数据统计:`);
    console.log(`   - Applications表记录数: ${applicationsCount}`);
    if (hasPaymentsTable) {
      console.log(`   - Payments表记录数: ${paymentsCount}`);
    }
    console.log(`   - Applications表包含支付字段: ${hasPaymentFields ? '是' : '否'}`);
    console.log();
    
    // 判断系统状态
    if (hasPaymentsTable && !hasPaymentFields) {
      // 老版本：有payments表，applications表没有支付字段
      return {
        needsUpgrade: true,
        isUpgraded: false,
        hasPaymentsTable: true,
        hasPaymentFields: false,
        paymentsCount,
        applicationsCount,
        riskLevel: paymentsCount > 0 ? 'high' : 'low'
      };
    } else if (!hasPaymentsTable && hasPaymentFields) {
      // 新版本：没有payments表，applications表有支付字段
      return {
        needsUpgrade: false,
        isUpgraded: true,
        hasPaymentsTable: false,
        hasPaymentFields: true,
        paymentsCount: 0,
        applicationsCount
      };
    } else if (hasPaymentsTable && hasPaymentFields) {
      // 过渡状态：两个表都存在，可能升级未完成
      return {
        needsUpgrade: true,
        isUpgraded: false,
        hasPaymentsTable: true,
        hasPaymentFields: true,
        paymentsCount,
        applicationsCount,
        riskLevel: 'medium',
        isPartialUpgrade: true
      };
    } else {
      // 全新安装：没有payments表，applications表也没有支付字段
      return {
        needsUpgrade: true,
        isUpgraded: false,
        hasPaymentsTable: false,
        hasPaymentFields: false,
        paymentsCount: 0,
        applicationsCount,
        riskLevel: 'low',
        isNewInstall: true
      };
    }
  }

  async performUpgrade(systemStatus) {
    if (systemStatus.isNewInstall) {
      console.log('🆕 检测到全新安装，只需添加支付字段...');
      await this.addPaymentFields();
      return;
    }

    if (systemStatus.riskLevel === 'high' && !this.force) {
      console.log('⚠️  检测到高风险升级（存在支付数据），建议先备份数据库');
      console.log('   如果确认要继续，请使用 --force 参数');
      return;
    }

    if (this.dryRun) {
      console.log('🔍 DRY RUN 模式 - 以下是将要执行的操作:');
      await this.showUpgradePreview(systemStatus);
      return;
    }

    // 创建备份
    if (systemStatus.paymentsCount > 0) {
      await this.createBackup();
    }

    // 执行升级
    await this.executeUpgrade(systemStatus);
  }

  async addPaymentFields() {
    console.log('📝 添加支付相关字段到applications表...');
    
    if (this.dryRun) {
      console.log('   [DRY RUN] 将添加支付相关字段');
      return;
    }

    const queryInterface = sequelize.getQueryInterface();
    const transaction = await sequelize.transaction();

    try {
      const fieldsToAdd = [
        ['paymentOrderId', { type: Sequelize.STRING(50), allowNull: true }],
        ['paymentMethod', { type: Sequelize.ENUM('wechat', 'alipay'), allowNull: true }],
        ['paymentStatus', { type: Sequelize.ENUM('pending', 'paid', 'failed', 'cancelled', 'expired'), allowNull: true }],
        ['thirdPartyOrderId', { type: Sequelize.STRING(100), allowNull: true }],
        ['thirdPartyTransactionId', { type: Sequelize.STRING(100), allowNull: true }],
        ['qrCodeUrl', { type: Sequelize.TEXT, allowNull: true }],
        ['qrCodeContent', { type: Sequelize.TEXT, allowNull: true }],
        ['paidAt', { type: Sequelize.DATE, allowNull: true }],
        ['paymentExpiresAt', { type: Sequelize.DATE, allowNull: true }],
        ['paymentCallbackData', { type: Sequelize.JSON, allowNull: true }],
        ['paymentFailureReason', { type: Sequelize.TEXT, allowNull: true }],
        ['refundStatus', { type: Sequelize.ENUM('none', 'pending', 'completed', 'failed'), defaultValue: 'none' }],
        ['refundAmount', { type: Sequelize.DECIMAL(10, 2), defaultValue: 0 }],
        ['refundedAt', { type: Sequelize.DATE, allowNull: true }],
        ['paymentClientIp', { type: Sequelize.STRING(45), allowNull: true }],
        ['paymentUserAgent', { type: Sequelize.TEXT, allowNull: true }]
      ];

      for (const [fieldName, fieldConfig] of fieldsToAdd) {
        try {
          await queryInterface.addColumn('applications', fieldName, fieldConfig, { transaction });
          console.log(`   ✅ 添加字段: ${fieldName}`);
        } catch (error) {
          if (error.message.includes('duplicate column name')) {
            console.log(`   ⏭️  字段已存在: ${fieldName}`);
          } else {
            throw error;
          }
        }
      }

      // 添加索引
      const indexesToAdd = [
        ['paymentOrderId'],
        ['paymentStatus'],
        ['paymentMethod'],
        ['thirdPartyOrderId'],
        ['thirdPartyTransactionId']
      ];

      for (const indexFields of indexesToAdd) {
        try {
          const indexName = `applications_${indexFields.join('_')}`;
          await queryInterface.addIndex('applications', indexFields, {
            name: indexName,
            transaction
          });
          console.log(`   ✅ 添加索引: ${indexName}`);
        } catch (error) {
          if (error.message.includes('already exists')) {
            console.log(`   ⏭️  索引已存在: applications_${indexFields.join('_')}`);
          } else {
            console.log(`   ⚠️  索引添加失败: ${error.message}`);
          }
        }
      }

      await transaction.commit();
      console.log('✅ 支付字段添加完成\n');

    } catch (error) {
      await transaction.rollback();
      throw new Error(`添加支付字段失败: ${error.message}`);
    }
  }

  async executeUpgrade(systemStatus) {
    console.log('🔄 执行数据迁移...');
    
    const transaction = await sequelize.transaction();
    
    try {
      // 1. 添加支付字段（如果还没有）
      if (!systemStatus.hasPaymentFields) {
        await this.addPaymentFields();
      }

      // 2. 迁移支付数据
      if (systemStatus.hasPaymentsTable && systemStatus.paymentsCount > 0) {
        await this.migratePaymentData(transaction);
      }

      // 3. 删除payments表
      if (systemStatus.hasPaymentsTable) {
        await this.dropPaymentsTable(transaction);
      }

      await transaction.commit();
      console.log('✅ 数据迁移完成\n');

    } catch (error) {
      await transaction.rollback();
      throw new Error(`数据迁移失败: ${error.message}`);
    }
  }

  async migratePaymentData(transaction) {
    console.log('📦 迁移支付数据...');
    
    const [payments] = await sequelize.query(
      'SELECT * FROM payments ORDER BY createdAt ASC',
      { transaction }
    );

    console.log(`   发现 ${payments.length} 条支付记录需要迁移`);

    for (const payment of payments) {
      await sequelize.query(`
        UPDATE applications 
        SET 
          paymentOrderId = :paymentId,
          paymentMethod = :paymentMethod,
          paymentStatus = :status,
          thirdPartyOrderId = :thirdPartyOrderId,
          thirdPartyTransactionId = :thirdPartyTransactionId,
          qrCodeUrl = :qrCodeUrl,
          qrCodeContent = :qrCodeContent,
          paidAt = :paidAt,
          paymentExpiresAt = :expiresAt,
          paymentCallbackData = :callbackData,
          paymentFailureReason = :failureReason,
          refundStatus = :refundStatus,
          refundAmount = :refundAmount,
          refundedAt = :refundedAt,
          paymentClientIp = :clientIp,
          paymentUserAgent = :userAgent
        WHERE id = :applicationId
      `, {
        replacements: {
          paymentId: payment.paymentId,
          paymentMethod: payment.paymentMethod,
          status: payment.status,
          thirdPartyOrderId: payment.thirdPartyOrderId,
          thirdPartyTransactionId: payment.thirdPartyTransactionId,
          qrCodeUrl: payment.qrCodeUrl,
          qrCodeContent: payment.qrCodeContent,
          paidAt: payment.paidAt,
          expiresAt: payment.expiresAt,
          callbackData: payment.callbackData ? JSON.stringify(payment.callbackData) : null,
          failureReason: payment.failureReason,
          refundStatus: payment.refundStatus || 'none',
          refundAmount: payment.refundAmount || 0,
          refundedAt: payment.refundedAt,
          clientIp: payment.clientIp,
          userAgent: payment.userAgent,
          applicationId: payment.applicationId
        },
        type: Sequelize.QueryTypes.UPDATE,
        transaction
      });
    }

    console.log('   ✅ 支付数据迁移完成');
  }

  async dropPaymentsTable(transaction) {
    console.log('🗑️  删除payments表...');
    
    const queryInterface = sequelize.getQueryInterface();
    await queryInterface.dropTable('payments', { transaction });
    
    console.log('   ✅ payments表已删除');
  }

  async createBackup() {
    console.log('💾 创建数据备份...');
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = `database-backup-${timestamp}.sql`;
    
    // 这里应该实现实际的备份逻辑
    // 对于SQLite，可以复制数据库文件
    // 对于MySQL/PostgreSQL，可以使用相应的备份命令
    
    console.log(`   ✅ 备份已创建: ${backupFile}`);
    this.backupCreated = true;
  }

  async showUpgradePreview(systemStatus) {
    console.log('📋 升级预览:');
    
    if (!systemStatus.hasPaymentFields) {
      console.log('   1. 添加支付相关字段到applications表');
    }
    
    if (systemStatus.hasPaymentsTable && systemStatus.paymentsCount > 0) {
      console.log(`   2. 迁移 ${systemStatus.paymentsCount} 条支付记录`);
    }
    
    if (systemStatus.hasPaymentsTable) {
      console.log('   3. 删除payments表');
    }
    
    console.log('\n要执行实际升级，请运行: node scripts/upgrade-payment-system.js');
  }

  async verifyUpgradedSystem() {
    console.log('🔍 验证升级后的系统...');
    
    const queryInterface = sequelize.getQueryInterface();
    const tables = await queryInterface.showAllTables();
    const applicationColumns = await queryInterface.describeTable('applications');
    
    const requiredFields = [
      'paymentOrderId', 'paymentMethod', 'paymentStatus',
      'thirdPartyOrderId', 'thirdPartyTransactionId',
      'qrCodeUrl', 'qrCodeContent', 'paidAt', 'paymentExpiresAt'
    ];
    
    const missingFields = requiredFields.filter(field => !(field in applicationColumns));
    
    if (missingFields.length > 0) {
      console.log('❌ 系统验证失败，缺少字段:', missingFields.join(', '));
    } else if (tables.includes('payments')) {
      console.log('⚠️  系统验证警告: payments表仍然存在');
    } else {
      console.log('✅ 系统验证通过，升级成功');
    }
  }
}

// 命令行参数处理
const args = process.argv.slice(2);
const options = {
  dryRun: args.includes('--dry-run'),
  force: args.includes('--force')
};

// 运行升级程序
const upgrader = new PaymentSystemUpgrader(options);
upgrader.run().catch(console.error);
