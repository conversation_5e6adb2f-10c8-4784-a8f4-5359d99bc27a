const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Report = sequelize.define('Report', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  applicationId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'applications',
      key: 'id'
    }
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  reportId: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    defaultValue: () => 'RPT' + Date.now().toString().slice(-6) + Math.random().toString(36).substring(2, 3).toUpperCase()
  },
  title: {
    type: DataTypes.STRING(500),
    allowNull: false
  },
  summary: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  // Report sections stored as JSON
  sections: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: []
  },
  // Analysis results stored as JSON
  analysisResults: {
    type: DataTypes.JSON,
    allowNull: true
  },
  // Visualizations stored as JSON
  visualizations: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: []
  },
  // Files stored as JSON
  files: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: []
  },
  // Metadata stored as JSON
  metadata: {
    type: DataTypes.JSON,
    allowNull: true
  },
  // Quality scores
  overallScore: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: true,
    validate: {
      min: 0,
      max: 1
    }
  },
  confidence: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: true,
    validate: {
      min: 0,
      max: 1
    }
  },
  completeness: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: true,
    validate: {
      min: 0,
      max: 1
    }
  },
  reliability: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: true,
    validate: {
      min: 0,
      max: 1
    }
  },
  // Access control
  isPublic: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  isPaid: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'USD'
  },
  downloadCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  lastDownloaded: {
    type: DataTypes.DATE,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('generating', 'ready', 'error'),
    defaultValue: 'generating'
  },
  generatedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  expiresAt: {
    type: DataTypes.DATE,
    defaultValue: () => new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year
  },
  tags: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: []
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'reports',
  timestamps: true,
  indexes: [
    {
      fields: ['applicationId']
    },
    {
      fields: ['userId', 'createdAt']
    },
    {
      unique: true,
      fields: ['reportId']
    },
    {
      fields: ['status']
    },
    {
      fields: ['isPublic']
    }
  ]
});

// Instance methods
Report.prototype.recordDownload = async function() {
  this.downloadCount += 1;
  this.lastDownloaded = new Date();
  return await this.save();
};

Report.prototype.isAccessible = function(user) {
  // Owner can always access
  if (this.userId === user.id) {
    return true;
  }

  // Public reports are accessible to all
  if (this.isPublic) {
    return true;
  }

  // Check if user has paid for access (implement payment logic)
  return false;
};

Report.prototype.generatePreview = function() {
  const analysisResults = this.analysisResults || {};
  return {
    reportId: this.reportId,
    title: this.title,
    summary: this.summary.substring(0, 500) + (this.summary.length > 500 ? '...' : ''),
    analysisResults: {
      proteinComplexes: (analysisResults.proteinComplexes || []).slice(0, 3),
      proteinInteractions: (analysisResults.proteinInteractions || []).slice(0, 5)
    },
    quality: {
      overallScore: this.overallScore,
      confidence: this.confidence,
      completeness: this.completeness,
      reliability: this.reliability
    },
    generatedAt: this.generatedAt
  };
};

Report.prototype.getAge = function() {
  return Math.floor((Date.now() - this.createdAt) / (1000 * 60 * 60 * 24)); // in days
};

module.exports = Report;
