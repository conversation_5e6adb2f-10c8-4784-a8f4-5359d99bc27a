@echo off
setlocal enabledelayedexpansion

echo 🧪 测试系统初始化功能
echo.

echo 📋 测试步骤:
echo 1. 检查系统初始化状态
echo 2. 访问初始化页面
echo 3. 创建管理员账户
echo 4. 测试管理员登录
echo.

echo 🔍 步骤1: 检查系统状态...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3001/api/system/init-status' -UseBasicParsing; Write-Host 'API响应:' $response.Content } catch { Write-Host '❌ API调用失败:' $_.Exception.Message }"

echo.
echo 🌐 步骤2: 打开初始化页面...
echo 正在打开浏览器到初始化页面...
start http://localhost:5173/system/init

echo.
echo 📝 步骤3: 手动测试说明
echo.
echo 请在浏览器中完成以下操作:
echo.
echo 🔧 在初始化页面:
echo    - 用户名: admin
echo    - 邮箱: <EMAIL>  
echo    - 密码: fHadmin
echo    - 确认密码: fHadmin
echo    - 姓名: Super (可选)
echo    - 名字: Admin (可选)
echo    - 点击 "🚀 初始化系统"
echo.
echo 🔐 在管理员登录页面:
echo    - 用户名: admin (应该自动填充)
echo    - 密码: fHadmin
echo    - 点击 "管理员登录"
echo.
echo 📊 在管理员仪表板:
echo    - 查看系统统计
echo    - 测试用户管理功能
echo    - 验证所有功能正常
echo.

echo ⏳ 等待您完成测试...
echo 完成测试后，按任意键检查最终状态...
pause > nul

echo.
echo 🔍 步骤4: 检查最终系统状态...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3001/api/system/init-status' -UseBasicParsing; Write-Host 'API响应:' $response.Content } catch { Write-Host '❌ API调用失败:' $_.Exception.Message }"

echo.
echo 🎯 测试完成！
echo.
echo 如果看到 "isInitialized":true 和 "adminCount":1，说明初始化成功！
echo.
pause
