// API Types for Quantix Application

import type { AxiosResponse } from 'axios'

// Base Types
export interface ApiResponse<T = any> {
  data: T
  message?: string
  success?: boolean
}

export interface PaginationParams {
  page?: number
  limit?: number
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginationResponse {
  page: number
  currentPage: number
  totalPages: number
  total: number
  limit?: number
  hasNext?: boolean
  hasPrev?: boolean
}

// User Types
export interface User {
  id: number
  email: string
  firstName: string
  lastName: string
  role: string
  isActive: boolean
  isEmailVerified: boolean
  organization?: string
  phone?: string
  country?: string
  language?: string
  subscriptionPlan?: string
  createdAt: string
  updatedAt: string
}

export interface LoginCredentials {
  email: string
  password: string
  captcha?: string
}

export interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  organization?: string
  phone?: string
  country?: string
  language?: string
  captcha?: string
  confirmPassword?: string
  role?: string
}

export interface AuthResponse {
  token: string
  user: User
  message: string
}

export interface VerificationData {
  email: string
  verificationCode: string
  code?: string
}

// Application Types
export interface Application {
  id: number
  applicationId: string
  userId?: number
  user?: User
  contactPhone?: string
  analysisType: string
  status: string
  progressPercentage?: number
  currentStep?: string
  files?: FileInfo[]
  parameters?: AnalysisParameters
  notes?: string
  estimatedCost?: number
  reportPrice?: number
  reportPaymentStatus?: string
  previewReportFile?: FileInfo
  fullReportFile?: FileInfo
  resultFiles?: FileInfo[]
  contactInfo?: ContactInfo
  createdAt: string
  updatedAt: string
  newStatus?: string
}

export interface FileInfo {
  filename?: string
  originalName: string
  size: number
  uploadedAt: string
  mimeType?: string
  type?: string
  fileType?: string
}

export interface AnalysisParameters {
  algorithm?: string
  confidence?: number
  maxIterations?: number
  [key: string]: any
}

export interface ContactInfo {
  phone?: string
  email?: string
}

// Payment Types
export interface PaymentData {
  applicationId: number
  amount: number
  paymentMethod: string
  description?: string
}

export interface Payment {
  paymentId: string
  amount: number
  paymentMethod: string
  status: string
  qrCodeContent?: string
  expiresAt?: string
  paidAt?: string
  qrCodeUrl?: string
}

// System Types
export interface SystemInitStatus {
  isInitialized: boolean
  adminCount: number
  message: string
}

export interface SystemInitData {
  email: string
  password: string
  firstName: string
  lastName: string
  organization?: string
  username?: string
}

// Admin Types
export interface DashboardStats {
  users: {
    total: number
    user: number
    admin: number
  }
  applications: {
    total: number
    pending: number
    completed: number
    failed: number
    processing: number
  }
  reports: {
    total: number
  }
  systemHealth: SystemHealth
}

export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical'
  uptime: number
  memoryUsage: number
  cpuUsage: number
}

// API Function Types
export interface AuthAPI {
  register: (data: RegisterData) => Promise<AxiosResponse<AuthResponse>>
  verifyEmail: (data: VerificationData) => Promise<AxiosResponse<AuthResponse>>
  resendVerification: (data: { email: string }) => Promise<AxiosResponse<ApiResponse>>
  login: (data: LoginCredentials) => Promise<AxiosResponse<AuthResponse>>
  logout: () => Promise<AxiosResponse<ApiResponse>>
  getCurrentUser: () => Promise<AxiosResponse<{ user: User }>>
}

export interface UserAPI {
  updateProfile: (data: { profile: Partial<User> }) => Promise<AxiosResponse<{ user: User }>>
  updatePreferences: (data: { preferences: any }) => Promise<AxiosResponse<{ preferences: any }>>
  changePassword: (data: { currentPassword: string; newPassword: string }) => Promise<AxiosResponse<ApiResponse>>
  getStats: () => Promise<AxiosResponse<ApiResponse>>
  deactivateAccount: (data: { password: string }) => Promise<AxiosResponse<ApiResponse>>
  deleteAccount: (data: { password: string }) => Promise<AxiosResponse<ApiResponse>>
}

export interface ApplicationAPI {
  submitApplication: (data: any) => Promise<AxiosResponse<{ application: Application }>>
  getApplications: (params?: PaginationParams) => Promise<AxiosResponse<{ applications: Application[]; pagination: PaginationResponse }>>
  getApplication: (id: number) => Promise<AxiosResponse<{ success?: boolean; application: Application }>>
  updateApplication: (id: number, data: Partial<Application>) => Promise<AxiosResponse<ApiResponse>>
  cancelApplication: (id: number) => Promise<AxiosResponse<ApiResponse>>
  deleteApplication: (id: number) => Promise<AxiosResponse<ApiResponse>>
  getApplicationStats: () => Promise<AxiosResponse<ApiResponse>>
  downloadReport: (id: number, reportType: 'preview' | 'full') => Promise<AxiosResponse<Blob>>
}

export interface UploadAPI {
  uploadFiles: (formData: FormData) => Promise<AxiosResponse<{ files: FileInfo[] }>>
  getFiles: () => Promise<AxiosResponse<{ files: FileInfo[] }>>
  getFileInfo: (filename: string) => Promise<AxiosResponse<{ file: FileInfo }>>
  downloadFile: (filename: string) => Promise<AxiosResponse<Blob>>
  deleteFile: (filename: string) => Promise<AxiosResponse<ApiResponse>>
  validateFile: (filename: string) => Promise<AxiosResponse<ApiResponse>>
}

export interface PaymentAPI {
  createPayment: (data: PaymentData) => Promise<AxiosResponse<{ success: boolean; payment: Payment; message?: string }>>
  getPaymentStatus: (paymentId: string) => Promise<AxiosResponse<{ success: boolean; payment: Payment }>>
  getQRCode: (paymentId: string) => Promise<AxiosResponse<ApiResponse>>
  cancelPayment: (paymentId: string) => Promise<AxiosResponse<ApiResponse>>
  simulatePayment: (paymentId: string) => Promise<AxiosResponse<{ success: boolean; payment: Payment; message?: string }>>
  getPaymentHistory: (params?: PaginationParams) => Promise<AxiosResponse<ApiResponse>>
  checkSimulateEnabled: () => Promise<AxiosResponse<{ enabled: boolean }>>
}

export interface SystemAPI {
  checkInitStatus: () => Promise<AxiosResponse<SystemInitStatus>>
  initialize: (data: SystemInitData) => Promise<AxiosResponse<{ user: User }>>
}

export interface AdminAPI {
  getDashboardStats: () => Promise<AxiosResponse<DashboardStats>>
  getSystemHealth: () => Promise<AxiosResponse<SystemHealth>>
  getUsers: (params?: PaginationParams) => Promise<AxiosResponse<{ users: User[]; pagination: PaginationResponse }>>
  createUser: (data: RegisterData) => Promise<AxiosResponse<{ user: User }>>
  updateUser: (id: number, data: Partial<User>) => Promise<AxiosResponse<ApiResponse>>
  deleteUser: (id: number) => Promise<AxiosResponse<ApiResponse>>
  toggleUserStatus: (id: number, action: 'activate' | 'deactivate') => Promise<AxiosResponse<ApiResponse>>
  resetUserPassword: (id: number, data: { newPassword: string }) => Promise<AxiosResponse<ApiResponse>>
  promoteUser: (id: number, data: { role: string }) => Promise<AxiosResponse<ApiResponse>>
  getApplications: (params?: PaginationParams) => Promise<AxiosResponse<{ applications: Application[]; pagination: PaginationResponse }>>
  getApplication: (id: number) => Promise<AxiosResponse<{ application: Application }>>
  updateApplicationStatus: (id: number, data: { status: string; notes?: string }) => Promise<AxiosResponse<ApiResponse>>
  deleteApplication: (id: number) => Promise<AxiosResponse<ApiResponse>>
  completeApplication: (id: number, formData: FormData) => Promise<AxiosResponse<ApiResponse>>
  uploadResultFiles: (id: number, formData: FormData) => Promise<AxiosResponse<ApiResponse>>
  downloadReport: (id: number, reportType: 'preview' | 'full') => Promise<AxiosResponse<Blob>>
}

// Error Types
export interface ApiError {
  status: number
  message: string
  details?: any
}

// Utility function types
export type SetAuthTokenFunction = (token: string | null) => void
export type GetAuthTokenFunction = () => string | null
export type IsAuthenticatedFunction = () => boolean
export type HandleApiErrorFunction = (error: any) => ApiError
