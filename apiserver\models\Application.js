const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Application = sequelize.define(
  'Application',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    applicationId: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      defaultValue: () =>
        'APP' +
        Date.now().toString().slice(-6) +
        Math.random().toString(36).substr(2, 3).toUpperCase()
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    contactPhone: {
      type: DataTypes.STRING(20),
      allowNull: false
    },
    contactEmail: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    // File information stored as JSON
    files: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: []
    },
    analysisType: {
      type: DataTypes.ENUM(
        'protein_complex',
        'protein_interaction',
        'structure_prediction',
        'drug_target'
      ),
      defaultValue: 'protein_complex'
    },
    // Analysis parameters stored as JSON
    parameters: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: {
        algorithm: 'alphafold',
        confidence: 0.7,
        maxIterations: 1000
      }
    },
    status: {
      type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed', 'cancelled'),
      defaultValue: 'pending'
    },
    // Progress information
    progressPercentage: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0,
        max: 100
      }
    },
    currentStep: {
      type: DataTypes.STRING(255),
      defaultValue: 'Queued'
    },
    estimatedTimeRemaining: {
      type: DataTypes.INTEGER, // in minutes
      allowNull: true
    },
    // Analysis results stored as JSON
    results: {
      type: DataTypes.JSON,
      allowNull: true
    },
    reportId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'reports',
        key: 'id'
      }
    },
    // Result files stored as JSON
    resultFiles: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: []
    },
    // Preview report file (free for users)
    previewReportFile: {
      type: DataTypes.JSON,
      allowNull: true
    },
    // Full report file (paid download)
    fullReportFile: {
      type: DataTypes.JSON,
      allowNull: true
    },
    // Payment status for full report
    reportPaymentStatus: {
      type: DataTypes.ENUM('unpaid', 'paid', 'free'),
      defaultValue: 'unpaid'
    },
    reportPrice: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0
    },
    // 支付订单号（系统生成，用于标识这个申请的支付订单）
    paymentOrderId: {
      type: DataTypes.STRING(50),
      allowNull: true,
      unique: true
    },
    // 支付方式：wechat（微信）、alipay（支付宝）
    paymentMethod: {
      type: DataTypes.ENUM('wechat', 'alipay'),
      allowNull: true
    },
    // 支付状态：pending（待支付）、paid（已支付）、failed（支付失败）、cancelled（已取消）、expired（已过期）
    paymentStatus: {
      type: DataTypes.ENUM('pending', 'paid', 'failed', 'cancelled', 'expired'),
      allowNull: true
    },
    // 第三方支付平台的订单号
    thirdPartyOrderId: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    // 第三方支付平台的交易号
    thirdPartyTransactionId: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    // 支付二维码URL
    qrCodeUrl: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    // 支付二维码内容（用于生成二维码）
    qrCodeContent: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    // 支付完成时间
    paidAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    // 支付过期时间（默认30分钟）
    paymentExpiresAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    // 支付回调数据（JSON格式存储第三方平台返回的数据）
    paymentCallbackData: {
      type: DataTypes.JSON,
      allowNull: true
    },
    // 支付失败原因
    paymentFailureReason: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    // 退款状态：none（无退款）、pending（退款中）、completed（已退款）、failed（退款失败）
    refundStatus: {
      type: DataTypes.ENUM('none', 'pending', 'completed', 'failed'),
      defaultValue: 'none'
    },
    // 退款金额
    refundAmount: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0
    },
    // 退款时间
    refundedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    // 客户端IP地址（支付时记录）
    paymentClientIp: {
      type: DataTypes.STRING(45),
      allowNull: true
    },
    // 用户代理信息（支付时记录）
    paymentUserAgent: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    priority: {
      type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
      defaultValue: 'normal'
    },
    estimatedCost: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0
    },
    actualCost: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0
    },
    processingStartedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    processingCompletedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    }
  },
  {
    tableName: 'applications',
    timestamps: true,
    indexes: [
      {
        fields: ['userId', 'createdAt']
      },
      {
        unique: true,
        fields: ['applicationId']
      },
      {
        fields: ['status']
      },
      {
        fields: ['contactEmail']
      },
      {
        fields: ['paymentOrderId']
      },
      {
        fields: ['paymentStatus']
      },
      {
        fields: ['paymentMethod']
      },
      {
        fields: ['thirdPartyOrderId']
      },
      {
        fields: ['thirdPartyTransactionId']
      },
      {
        fields: ['reportPaymentStatus']
      }
    ]
  }
);

// Generate application ID before creating
Application.beforeCreate(async (application) => {
  if (!application.applicationId) {
    application.applicationId =
      'APP' +
      Date.now().toString().slice(-6) +
      Math.random().toString(36).substring(2, 5).toUpperCase();
  }
});

// Instance methods
Application.prototype.updateProgress = async function (percentage, step, estimatedTime) {
  this.progressPercentage = percentage;
  this.currentStep = step;
  if (estimatedTime !== undefined) {
    this.estimatedTimeRemaining = estimatedTime;
  }
  return await this.save();
};

Application.prototype.startProcessing = async function () {
  this.status = 'processing';
  this.processingStartedAt = new Date();
  this.progressPercentage = 0;
  this.currentStep = 'Initializing';
  return await this.save();
};

Application.prototype.complete = async function (results) {
  this.status = 'completed';
  this.processingCompletedAt = new Date();
  this.progressPercentage = 100;
  this.currentStep = 'Completed';
  if (results) {
    this.results = { ...this.results, ...results };
  }
  return await this.save();
};

Application.prototype.fail = async function (error) {
  this.status = 'failed';
  this.processingCompletedAt = new Date();
  this.currentStep = `Failed: ${error}`;
  return await this.save();
};

// Virtual for processing duration
Application.prototype.getProcessingDuration = function () {
  if (this.processingStartedAt && this.processingCompletedAt) {
    return Math.round((this.processingCompletedAt - this.processingStartedAt) / (1000 * 60)); // in minutes
  }
  return null;
};

// 支付相关实例方法
Application.prototype.generatePaymentOrderId = function () {
  if (!this.paymentOrderId) {
    this.paymentOrderId = 'PAY' +
      Date.now().toString() +
      Math.random().toString(36).substring(2, 6).toUpperCase();
  }
  return this.paymentOrderId;
};

Application.prototype.isPaymentExpired = function () {
  return this.paymentExpiresAt && new Date() > this.paymentExpiresAt;
};

Application.prototype.canBePaid = function () {
  return this.paymentStatus === 'pending' && !this.isPaymentExpired();
};

Application.prototype.canBeRefunded = function () {
  return this.paymentStatus === 'paid' && this.refundStatus === 'none';
};

Application.prototype.initializePayment = function (paymentMethod, clientIp, userAgent) {
  this.generatePaymentOrderId();
  this.paymentMethod = paymentMethod;
  this.paymentStatus = 'pending';
  this.paymentExpiresAt = new Date(Date.now() + 30 * 60 * 1000); // 30分钟后过期
  this.paymentClientIp = clientIp;
  this.paymentUserAgent = userAgent;
  this.paidAt = null;
  this.paymentFailureReason = null;
  return this;
};

Application.prototype.markPaymentPaid = function (transactionId, callbackData) {
  this.paymentStatus = 'paid';
  this.reportPaymentStatus = 'paid';
  this.paidAt = new Date();
  this.thirdPartyTransactionId = transactionId;
  this.paymentCallbackData = callbackData;
  return this;
};

Application.prototype.markPaymentFailed = function (reason) {
  this.paymentStatus = 'failed';
  this.paymentFailureReason = reason;
  return this;
};

Application.prototype.markPaymentExpired = function () {
  if (this.paymentStatus === 'pending' && this.isPaymentExpired()) {
    this.paymentStatus = 'expired';
  }
  return this;
};

// 静态方法：根据第三方订单号查找申请
Application.findByThirdPartyOrderId = function (thirdPartyOrderId) {
  return this.findOne({
    where: {
      thirdPartyOrderId: thirdPartyOrderId
    }
  });
};

// 静态方法：根据支付订单号查找申请
Application.findByPaymentOrderId = function (paymentOrderId) {
  return this.findOne({
    where: {
      paymentOrderId: paymentOrderId
    }
  });
};

module.exports = Application;
