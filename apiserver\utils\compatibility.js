/**
 * 系统兼容性检查和自动修复工具
 * 
 * 此模块在系统启动时自动检查数据库结构的兼容性，
 * 并在必要时进行自动修复或提示用户手动升级。
 */

const { sequelize } = require('../models');
const { Sequelize } = require('sequelize');

class CompatibilityChecker {
  constructor() {
    this.issues = [];
    this.autoFixEnabled = process.env.AUTO_FIX_COMPATIBILITY === 'true';
  }

  /**
   * 执行兼容性检查
   * @returns {Promise<{compatible: boolean, issues: Array, autoFixed: boolean}>}
   */
  async check() {
    console.log('🔍 正在检查系统兼容性...');
    
    try {
      await this.checkDatabaseConnection();
      await this.checkPaymentSystemCompatibility();
      await this.checkModelCompatibility();
      
      const hasIssues = this.issues.length > 0;
      const autoFixed = hasIssues && this.autoFixEnabled;
      
      if (hasIssues && this.autoFixEnabled) {
        await this.autoFix();
      }
      
      return {
        compatible: !hasIssues || autoFixed,
        issues: this.issues,
        autoFixed: autoFixed
      };
      
    } catch (error) {
      console.error('❌ 兼容性检查失败:', error.message);
      return {
        compatible: false,
        issues: [{ type: 'critical', message: error.message }],
        autoFixed: false
      };
    }
  }

  async checkDatabaseConnection() {
    try {
      await sequelize.authenticate();
    } catch (error) {
      this.addIssue('critical', '数据库连接失败', error.message);
    }
  }

  async checkPaymentSystemCompatibility() {
    const queryInterface = sequelize.getQueryInterface();
    
    try {
      const tables = await queryInterface.showAllTables();
      const hasPaymentsTable = tables.includes('payments');
      const hasApplicationsTable = tables.includes('applications');
      
      if (!hasApplicationsTable) {
        this.addIssue('critical', '缺少applications表', '系统核心表不存在');
        return;
      }
      
      const applicationColumns = await queryInterface.describeTable('applications');
      const hasPaymentFields = 'paymentOrderId' in applicationColumns;
      
      // 检查支付系统状态
      if (hasPaymentsTable && !hasPaymentFields) {
        // 老版本系统，需要升级
        const [paymentResults] = await sequelize.query('SELECT COUNT(*) as count FROM payments');
        const paymentCount = paymentResults[0].count;
        
        this.addIssue('upgrade', '检测到老版本支付系统', 
          `发现独立的payments表（${paymentCount}条记录），需要升级到新版本`);
          
      } else if (hasPaymentsTable && hasPaymentFields) {
        // 升级未完成状态
        this.addIssue('warning', '支付系统升级未完成', 
          '同时存在payments表和新的支付字段，可能升级中断');
          
      } else if (!hasPaymentsTable && !hasPaymentFields) {
        // 全新安装，需要添加支付字段
        this.addIssue('setup', '需要初始化支付系统', 
          '检测到全新安装，需要添加支付相关字段');
      }
      
      // 检查必需的支付字段
      if (hasPaymentFields) {
        const requiredPaymentFields = [
          'paymentOrderId', 'paymentMethod', 'paymentStatus',
          'thirdPartyOrderId', 'thirdPartyTransactionId',
          'qrCodeUrl', 'qrCodeContent', 'paidAt', 'paymentExpiresAt',
          'paymentCallbackData', 'paymentFailureReason',
          'refundStatus', 'refundAmount', 'refundedAt',
          'paymentClientIp', 'paymentUserAgent'
        ];
        
        const missingFields = requiredPaymentFields.filter(field => !(field in applicationColumns));
        
        if (missingFields.length > 0) {
          this.addIssue('warning', '支付字段不完整', 
            `缺少字段: ${missingFields.join(', ')}`);
        }
      }
      
    } catch (error) {
      this.addIssue('error', '支付系统检查失败', error.message);
    }
  }

  async checkModelCompatibility() {
    try {
      // 检查模型定义与数据库结构的一致性
      const { Application } = require('../models');
      
      // 尝试执行一个简单的查询来验证模型兼容性
      await Application.findOne({ limit: 1 });
      
    } catch (error) {
      if (error.message.includes('no such column')) {
        this.addIssue('error', '模型与数据库结构不匹配', 
          '模型定义的字段在数据库中不存在，可能需要运行迁移');
      } else {
        this.addIssue('warning', '模型兼容性检查失败', error.message);
      }
    }
  }

  async autoFix() {
    console.log('🔧 正在尝试自动修复兼容性问题...');
    
    for (const issue of this.issues) {
      try {
        switch (issue.type) {
          case 'setup':
            await this.autoFixSetup();
            break;
          case 'warning':
            if (issue.title.includes('支付字段不完整')) {
              await this.autoFixMissingFields();
            }
            break;
          default:
            console.log(`⏭️  跳过自动修复: ${issue.title} (类型: ${issue.type})`);
        }
      } catch (error) {
        console.error(`❌ 自动修复失败 [${issue.title}]:`, error.message);
      }
    }
  }

  async autoFixSetup() {
    console.log('🔧 自动修复: 初始化支付系统...');
    
    const queryInterface = sequelize.getQueryInterface();
    const transaction = await sequelize.transaction();
    
    try {
      const fieldsToAdd = [
        ['paymentOrderId', { type: Sequelize.STRING(50), allowNull: true }],
        ['paymentMethod', { type: Sequelize.ENUM('wechat', 'alipay'), allowNull: true }],
        ['paymentStatus', { type: Sequelize.ENUM('pending', 'paid', 'failed', 'cancelled', 'expired'), allowNull: true }],
        ['thirdPartyOrderId', { type: Sequelize.STRING(100), allowNull: true }],
        ['thirdPartyTransactionId', { type: Sequelize.STRING(100), allowNull: true }],
        ['qrCodeUrl', { type: Sequelize.TEXT, allowNull: true }],
        ['qrCodeContent', { type: Sequelize.TEXT, allowNull: true }],
        ['paidAt', { type: Sequelize.DATE, allowNull: true }],
        ['paymentExpiresAt', { type: Sequelize.DATE, allowNull: true }],
        ['paymentCallbackData', { type: Sequelize.JSON, allowNull: true }],
        ['paymentFailureReason', { type: Sequelize.TEXT, allowNull: true }],
        ['refundStatus', { type: Sequelize.ENUM('none', 'pending', 'completed', 'failed'), defaultValue: 'none' }],
        ['refundAmount', { type: Sequelize.DECIMAL(10, 2), defaultValue: 0 }],
        ['refundedAt', { type: Sequelize.DATE, allowNull: true }],
        ['paymentClientIp', { type: Sequelize.STRING(45), allowNull: true }],
        ['paymentUserAgent', { type: Sequelize.TEXT, allowNull: true }]
      ];

      for (const [fieldName, fieldConfig] of fieldsToAdd) {
        try {
          await queryInterface.addColumn('applications', fieldName, fieldConfig, { transaction });
        } catch (error) {
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
        }
      }

      await transaction.commit();
      console.log('✅ 支付系统初始化完成');
      
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async autoFixMissingFields() {
    console.log('🔧 自动修复: 添加缺失的支付字段...');
    // 实现添加缺失字段的逻辑
    await this.autoFixSetup(); // 复用相同的逻辑
  }

  addIssue(type, title, description) {
    this.issues.push({
      type,
      title,
      description,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 生成兼容性报告
   */
  generateReport(result) {
    console.log('\n📊 兼容性检查报告:');
    console.log('='.repeat(50));
    
    if (result.compatible) {
      console.log('✅ 系统兼容性: 正常');
      if (result.autoFixed) {
        console.log('🔧 自动修复: 已执行');
      }
    } else {
      console.log('❌ 系统兼容性: 存在问题');
    }
    
    if (result.issues.length > 0) {
      console.log('\n📋 发现的问题:');
      result.issues.forEach((issue, index) => {
        const icon = this.getIssueIcon(issue.type);
        console.log(`${index + 1}. ${icon} ${issue.title}`);
        console.log(`   ${issue.description}`);
      });
    }
    
    if (!result.compatible && !result.autoFixed) {
      console.log('\n🔧 建议的解决方案:');
      console.log('1. 运行升级脚本: node scripts/upgrade-payment-system.js --dry-run');
      console.log('2. 检查升级预览后执行: node scripts/upgrade-payment-system.js');
      console.log('3. 或启用自动修复: 设置环境变量 AUTO_FIX_COMPATIBILITY=true');
    }
    
    console.log('='.repeat(50));
  }

  getIssueIcon(type) {
    const icons = {
      'critical': '🚨',
      'error': '❌',
      'warning': '⚠️',
      'upgrade': '🔄',
      'setup': '🔧'
    };
    return icons[type] || '❓';
  }
}

/**
 * 快速兼容性检查（用于系统启动时）
 */
async function quickCompatibilityCheck() {
  const checker = new CompatibilityChecker();
  const result = await checker.check();
  
  if (!result.compatible && !result.autoFixed) {
    checker.generateReport(result);
    
    // 如果有严重问题，阻止系统启动
    const hasCriticalIssues = result.issues.some(issue => issue.type === 'critical');
    if (hasCriticalIssues) {
      console.error('\n🚨 检测到严重兼容性问题，系统无法启动');
      console.error('请先解决上述问题后再启动系统');
      process.exit(1);
    }
    
    // 对于非严重问题，给出警告但允许继续
    console.warn('\n⚠️  检测到兼容性问题，系统可能无法正常工作');
    console.warn('建议尽快解决上述问题');
  } else if (result.autoFixed) {
    console.log('✅ 兼容性问题已自动修复');
  }
  
  return result;
}

/**
 * 详细兼容性检查（用于手动执行）
 */
async function fullCompatibilityCheck() {
  const checker = new CompatibilityChecker();
  const result = await checker.check();
  checker.generateReport(result);
  return result;
}

module.exports = {
  CompatibilityChecker,
  quickCompatibilityCheck,
  fullCompatibilityCheck
};
