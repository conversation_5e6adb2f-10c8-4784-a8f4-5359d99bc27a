// TypeScript declarations for apiService.js

import {
  AuthAPI,
  UserAPI,
  UploadAPI,
  ApplicationAPI,
  PaymentAPI,
  SystemAPI,
  AdminAPI,
  SetAuthTokenFunction,
  GetAuthTokenFunction,
  IsAuthenticatedFunction,
  HandleApiErrorFunction
} from '@/types/api'

// API exports (these will be either real or mock based on configuration)
export const authAPI: AuthAPI
export const userAPI: UserAPI
export const uploadAPI: UploadAPI
export const applicationAPI: ApplicationAPI
export const paymentAPI: PaymentAPI
export const systemAPI: SystemAPI
export const adminAPI: AdminAPI
export const chatAPI: {
  sendMessage: (data: any) => Promise<any>
  sendPublicMessage: (data: any) => Promise<any>
  getSuggestions: (language: string) => Promise<any>
  getChatHistory: (params?: any) => Promise<any>
  clearChatHistory: () => Promise<any>
  getChatStats: () => Promise<any>
  getChatHealth: () => Promise<any>
}
export const healthAPI: {
  getHealth: () => Promise<any>
}

// Utility functions (these are always from the real API)
export const setAuthToken: SetAuthTokenFunction
export const getAuthToken: GetAuthTokenFunction
export const isAuthenticated: IsAuthenticatedFunction
export const handleApiError: HandleApiErrorFunction
