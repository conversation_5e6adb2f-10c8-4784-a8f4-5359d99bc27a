<template>
  <div class="test-container">
    <header class="test-header">
      <h1>🧪 Mock API 测试页面</h1>
      <p>测试 Mock API 是否正常工作</p>
      <div class="api-status">
        <span :class="['status-indicator', useMockApi ? 'mock' : 'real']">
          {{ useMockApi ? '🎭 使用 Mock API' : '🌐 使用真实 API' }}
        </span>
      </div>
    </header>

    <div class="test-content">
      <div class="test-section">
        <h2>🔐 认证测试</h2>
        <button @click="testLogin" class="test-btn">测试登录</button>
        <button @click="testSystemInit" class="test-btn">测试系统初始化检查</button>
      </div>

      <div class="test-section">
        <h2>📋 应用测试</h2>
        <button @click="testGetApplications" class="test-btn">获取应用列表</button>
        <button @click="testSubmitApplication" class="test-btn">提交应用</button>
      </div>

      <div class="test-section">
        <h2>💳 支付测试</h2>
        <button @click="testCreatePayment" class="test-btn">创建支付</button>
        <button @click="testSimulatePayment" class="test-btn">模拟支付成功</button>
      </div>

      <div class="results-section">
        <h2>📊 测试结果</h2>
        <div class="results-container">
          <div v-for="(result, index) in testResults" :key="index" 
               :class="['result-item', result.success ? 'success' : 'error']">
            <div class="result-header">
              <span class="result-method">{{ result.method }}</span>
              <span class="result-time">{{ result.time }}</span>
              <span :class="['result-status', result.success ? 'success' : 'error']">
                {{ result.success ? '✅ 成功' : '❌ 失败' }}
              </span>
            </div>
            <div class="result-data">
              <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
            </div>
          </div>
        </div>
        <button @click="clearResults" class="clear-btn">清空结果</button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import { authAPI, systemAPI, applicationAPI, paymentAPI } from '@/services/apiService'

interface TestResult {
  method: string
  time: string
  success: boolean
  data: any
}

export default defineComponent({
  name: 'MockApiTestView',
  setup() {
    const testResults = ref<TestResult[]>([])
    
    const useMockApi = computed(() => {
      return import.meta.env.VITE_USE_MOCK_API === 'true'
    })

    const addResult = (method: string, success: boolean, data: any) => {
      testResults.value.unshift({
        method,
        time: new Date().toLocaleTimeString(),
        success,
        data
      })
      // 只保留最近10个结果
      if (testResults.value.length > 10) {
        testResults.value = testResults.value.slice(0, 10)
      }
    }

    const testLogin = async () => {
      try {
        const result = await authAPI.login({
          email: '<EMAIL>',
          password: 'password123'
        })
        addResult('登录测试', true, result.data)
      } catch (error) {
        addResult('登录测试', false, error)
      }
    }

    const testSystemInit = async () => {
      try {
        const result = await systemAPI.checkInitStatus()
        addResult('系统初始化检查', true, result.data)
      } catch (error) {
        addResult('系统初始化检查', false, error)
      }
    }

    const testGetApplications = async () => {
      try {
        const result = await applicationAPI.getApplications({})
        addResult('获取应用列表', true, result.data)
      } catch (error) {
        addResult('获取应用列表', false, error)
      }
    }

    const testSubmitApplication = async () => {
      try {
        const result = await applicationAPI.submitApplication({
          contactInfo: { phone: '************' },
          analysisType: 'protein_complex',
          files: [{ filename: 'test.fasta', originalName: 'test.fasta' }],
          notes: 'Test application'
        })
        addResult('提交应用', true, result.data)
      } catch (error) {
        addResult('提交应用', false, error)
      }
    }

    const testCreatePayment = async () => {
      try {
        const result = await paymentAPI.createPayment({
          applicationId: 1,
          amount: 99.99,
          paymentMethod: 'wechat'
        })
        addResult('创建支付', true, result.data)
      } catch (error) {
        addResult('创建支付', false, error)
      }
    }

    const testSimulatePayment = async () => {
      try {
        const result = await paymentAPI.simulatePayment('PAY-123456')
        addResult('模拟支付成功', true, result.data)
      } catch (error) {
        addResult('模拟支付成功', false, error)
      }
    }

    const clearResults = () => {
      testResults.value = []
    }

    return {
      testResults,
      useMockApi,
      testLogin,
      testSystemInit,
      testGetApplications,
      testSubmitApplication,
      testCreatePayment,
      testSimulatePayment,
      clearResults
    }
  }
})
</script>

<style scoped>
.test-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.test-header h1 {
  color: #333;
  margin-bottom: 10px;
}

.api-status {
  margin-top: 15px;
}

.status-indicator {
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
}

.status-indicator.mock {
  background: #e3f2fd;
  color: #1976d2;
}

.status-indicator.real {
  background: #e8f5e8;
  color: #388e3c;
}

.test-content {
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  background: white;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.test-section h2 {
  margin-bottom: 15px;
  color: #333;
}

.test-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  margin: 5px;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s;
}

.test-btn:hover {
  background: #0056b3;
}

.results-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.results-container {
  max-height: 600px;
  overflow-y: auto;
}

.result-item {
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 10px;
  padding: 15px;
}

.result-item.success {
  border-left: 4px solid #28a745;
}

.result-item.error {
  border-left: 4px solid #dc3545;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.result-method {
  font-weight: bold;
  color: #333;
}

.result-time {
  color: #666;
  font-size: 0.9em;
}

.result-status.success {
  color: #28a745;
}

.result-status.error {
  color: #dc3545;
}

.result-data {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 3px;
  font-family: monospace;
  font-size: 0.9em;
  max-height: 200px;
  overflow-y: auto;
}

.clear-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 15px;
}

.clear-btn:hover {
  background: #545b62;
}
</style>
