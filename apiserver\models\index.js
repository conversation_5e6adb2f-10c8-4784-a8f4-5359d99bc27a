const { sequelize } = require('../config/database');
const User = require('./User');
const Application = require('./Application');
const Report = require('./Report');

// Define associations
User.hasMany(Application, {
  foreignKey: 'userId',
  as: 'applications'
});

Application.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
});

User.hasMany(Report, {
  foreignKey: 'userId',
  as: 'reports'
});

Report.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
});

Application.hasOne(Report, {
  foreignKey: 'applicationId',
  as: 'report'
});

Report.belongsTo(Application, {
  foreignKey: 'applicationId',
  as: 'application'
});

module.exports = {
  sequelize,
  User,
  Application,
  Report
};
