const express = require('express');
const { query, validationResult } = require('express-validator');
const Report = require('../models/Report');
const Application = require('../models/Application');
const { authenticate } = require('../middleware/auth');
const fs = require('fs');

const router = express.Router();

// Get report by application ID
router.get('/application/:applicationId', authenticate, async (req, res) => {
  try {
    const { applicationId } = req.params;

    // Find the application first
    const application = await Application.findOne({
      applicationId: applicationId,
      user: req.user._id,
      isDeleted: false
    });

    if (!application) {
      return res.status(404).json({
        error: 'Application not found',
        message: 'The requested application does not exist or you do not have access to it'
      });
    }

    // Find the report
    const report = await Report.findOne({
      application: application._id
    });

    if (!report) {
      return res.status(404).json({
        error: 'Report not found',
        message: 'No report found for this application. The analysis may still be in progress.'
      });
    }

    // Check if user has access to the full report
    const hasFullAccess = report.isAccessible(req.user);

    if (hasFullAccess) {
      // Return full report
      res.json({
        report: {
          id: report._id,
          reportId: report.reportId,
          title: report.title,
          summary: report.summary,
          sections: report.sections,
          analysisResults: report.analysisResults,
          visualizations: report.visualizations,
          files: report.files,
          metadata: report.metadata,
          quality: report.quality,
          access: report.access,
          status: report.status,
          generatedAt: report.generatedAt,
          tags: report.tags
        }
      });
    } else {
      // Return preview only
      const preview = report.generatePreview();
      res.json({
        report: {
          ...preview,
          access: {
            isPublic: report.access.isPublic,
            isPaid: report.access.isPaid,
            price: report.access.price,
            currency: report.access.currency
          },
          isPreview: true,
          message: 'This is a preview. Full report requires payment.'
        }
      });
    }

  } catch (error) {
    console.error('Get report error:', error);
    res.status(500).json({
      error: 'Failed to retrieve report',
      message: 'An error occurred while retrieving the report'
    });
  }
});

// Get report by report ID
router.get('/:reportId', authenticate, async (req, res) => {
  try {
    const { reportId } = req.params;

    const report = await Report.findOne({
      reportId: reportId
    }).populate('application', 'applicationId user');

    if (!report) {
      return res.status(404).json({
        error: 'Report not found',
        message: 'The requested report does not exist'
      });
    }

    // Check if user has access
    const hasFullAccess = report.isAccessible(req.user);

    if (!hasFullAccess && report.application.user.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You do not have access to this report'
      });
    }

    if (hasFullAccess) {
      // Return full report
      res.json({
        report: {
          id: report._id,
          reportId: report.reportId,
          title: report.title,
          summary: report.summary,
          sections: report.sections,
          analysisResults: report.analysisResults,
          visualizations: report.visualizations,
          files: report.files,
          metadata: report.metadata,
          quality: report.quality,
          access: report.access,
          status: report.status,
          generatedAt: report.generatedAt,
          tags: report.tags
        }
      });
    } else {
      // Return preview only
      const preview = report.generatePreview();
      res.json({
        report: {
          ...preview,
          access: {
            isPublic: report.access.isPublic,
            isPaid: report.access.isPaid,
            price: report.access.price,
            currency: report.access.currency
          },
          isPreview: true,
          message: 'This is a preview. Full report requires payment.'
        }
      });
    }

  } catch (error) {
    console.error('Get report by ID error:', error);
    res.status(500).json({
      error: 'Failed to retrieve report',
      message: 'An error occurred while retrieving the report'
    });
  }
});

// Download report file
router.get('/:reportId/download', authenticate, async (req, res) => {
  try {
    const { reportId } = req.params;
    const { format = 'pdf' } = req.query;

    const report = await Report.findOne({
      reportId: reportId
    }).populate('application', 'user');

    if (!report) {
      return res.status(404).json({
        error: 'Report not found',
        message: 'The requested report does not exist'
      });
    }

    // Check if user has access
    const hasFullAccess = report.isAccessible(req.user);

    if (!hasFullAccess) {
      return res.status(403).json({
        error: 'Payment required',
        message: 'Full report download requires payment',
        price: report.access.price,
        currency: report.access.currency
      });
    }

    // Find the report file
    const reportFile = report.files.find(file => file.type === format);

    if (!reportFile) {
      return res.status(404).json({
        error: 'File not found',
        message: `Report file in ${format} format is not available`
      });
    }

    const filePath = reportFile.path;

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        error: 'File not found',
        message: 'The report file does not exist on the server'
      });
    }

    // Record download
    await report.recordDownload();

    // Send file
    res.download(filePath, `${report.reportId}_report.${format}`, (error) => {
      if (error) {
        console.error('Report download error:', error);
        if (!res.headersSent) {
          res.status(500).json({
            error: 'Download failed',
            message: 'An error occurred during file download'
          });
        }
      }
    });

  } catch (error) {
    console.error('Download report error:', error);
    res.status(500).json({
      error: 'Download failed',
      message: 'An error occurred while preparing report download'
    });
  }
});

// Get user's reports
router.get('/',
  authenticate,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('status').optional().isIn(['generating', 'ready', 'error']).withMessage('Invalid status')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      // Build filter
      const filter = { user: req.user._id };

      if (req.query.status) {
        filter.status = req.query.status;
      }

      // Get reports with pagination
      const reports = await Report.find(filter)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate('application', 'applicationId analysisType')
        .lean();

      const totalCount = await Report.countDocuments(filter);
      const totalPages = Math.ceil(totalCount / limit);

      // Format response
      const formattedReports = reports.map(report => ({
        id: report._id,
        reportId: report.reportId,
        title: report.title,
        summary: report.summary.substring(0, 200) + (report.summary.length > 200 ? '...' : ''),
        status: report.status,
        quality: report.quality,
        access: report.access,
        generatedAt: report.generatedAt,
        application: {
          id: report.application._id,
          applicationId: report.application.applicationId,
          analysisType: report.application.analysisType
        }
      }));

      res.json({
        reports: formattedReports,
        pagination: {
          currentPage: page,
          totalPages: totalPages,
          totalCount: totalCount,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      });

    } catch (error) {
      console.error('Get reports error:', error);
      res.status(500).json({
        error: 'Failed to retrieve reports',
        message: 'An error occurred while retrieving reports'
      });
    }
  }
);

module.exports = router;
