const express = require('express');
const { query, body, validationResult } = require('express-validator');
const { User, Application, Report, sequelize } = require('../models');
const { Op } = require('sequelize');
const { authenticate, requireAdmin, requireSuperAdmin } = require('../middleware/auth');
const { upload } = require('../middleware/upload');
const _bcrypt = require('bcryptjs');

const router = express.Router();

// All admin routes require authentication and admin role
router.use(authenticate);
router.use(requireAdmin);

// Get dashboard statistics
router.get('/dashboard/stats', async (req, res) => {
  try {
    const [userStats, applicationStats, reportStats] = await Promise.all([
      User.findAll({
        attributes: ['role', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
        group: ['role'],
        raw: true
      }),
      Application.findAll({
        attributes: ['status', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
        group: ['status'],
        raw: true
      }),
      Report.findAll({
        attributes: ['status', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
        group: ['status'],
        raw: true
      })
    ]);

    const stats = {
      users: {
        total: 0,
        admin: 0,
        user: 0
      },
      applications: {
        total: 0,
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0,
        cancelled: 0
      },
      reports: {
        total: 0,
        generating: 0,
        ready: 0,
        error: 0
      }
    };

    // Process user stats
    userStats.forEach((stat) => {
      stats.users[stat.role] = parseInt(stat.count);
      stats.users.total += parseInt(stat.count);
    });

    // Process application stats
    applicationStats.forEach((stat) => {
      stats.applications[stat.status] = parseInt(stat.count);
      stats.applications.total += parseInt(stat.count);
    });

    // Process report stats
    reportStats.forEach((stat) => {
      stats.reports[stat.status] = parseInt(stat.count);
      stats.reports.total += parseInt(stat.count);
    });

    res.json({ stats });
  } catch (error) {
    console.error('Get admin stats error:', error);
    res.status(500).json({
      error: 'Failed to retrieve statistics',
      message: 'An error occurred while retrieving admin statistics'
    });
  }
});

// Get all users with pagination
router.get(
  '/users',
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('role').optional().isIn(['user', 'admin', 'superadmin']).withMessage('Invalid role'),
    query('status').optional().isIn(['active', 'inactive']).withMessage('Invalid status'),
    query('search').optional().isString().withMessage('Search must be a string')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;
      const skip = (page - 1) * limit;

      // Build filter
      const where = {};
      if (req.query.role) {
        where.role = req.query.role;
      }
      if (req.query.status) {
        where.isActive = req.query.status === 'active';
      }
      if (req.query.search) {
        where[Op.or] = [
          { email: { [Op.like]: `%${req.query.search}%` } },
          { firstName: { [Op.like]: `%${req.query.search}%` } },
          { lastName: { [Op.like]: `%${req.query.search}%` } }
        ];
      }

      const { count, rows: users } = await User.findAndCountAll({
        where,
        offset: skip,
        limit,
        order: [['createdAt', 'DESC']],
        attributes: { exclude: ['password'] }
      });

      const totalPages = Math.ceil(count / limit);

      res.json({
        users,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount: count,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      });
    } catch (error) {
      console.error('Get users error:', error);
      res.status(500).json({
        error: 'Failed to retrieve users',
        message: 'An error occurred while retrieving users'
      });
    }
  }
);

// Get all applications with pagination
router.get(
  '/applications',
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('status')
      .optional()
      .isIn(['pending', 'processing', 'completed', 'failed', 'cancelled'])
      .withMessage('Invalid status')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;
      const skip = (page - 1) * limit;

      // Build filter
      const where = { isDeleted: false };
      if (req.query.status) {
        where.status = req.query.status;
      }

      const { count, rows: applications } = await Application.findAndCountAll({
        where,
        offset: skip,
        limit,
        order: [['createdAt', 'DESC']],
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'email', 'firstName', 'lastName']
          }
        ]
      });

      const totalPages = Math.ceil(count / limit);

      res.json({
        applications,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount: count,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      });
    } catch (error) {
      console.error('Get applications error:', error);
      res.status(500).json({
        error: 'Failed to retrieve applications',
        message: 'An error occurred while retrieving applications'
      });
    }
  }
);

// Update application status (admin only)
router.patch(
  '/applications/:id/status',
  [
    body('status')
      .isIn(['pending', 'processing', 'completed', 'failed', 'cancelled'])
      .withMessage('Invalid status'),
    body('notes').optional().isString().withMessage('Notes must be a string')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { id } = req.params;
      const { status, notes } = req.body;

      const application = await Application.findByPk(id, {
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'email', 'firstName', 'lastName']
          }
        ]
      });

      if (!application) {
        return res.status(404).json({
          error: 'Application not found',
          message: 'The specified application does not exist'
        });
      }

      // Update application
      application.status = status;
      if (notes) {
        application.notes = notes;
      }
      application.updatedAt = new Date();

      await application.save();

      console.log(
        `📋 Admin ${req.user.email} updated application ${application.applicationId} status to ${status}`
      );

      res.json({
        message: 'Application status updated successfully',
        application: {
          id: application.id,
          applicationId: application.applicationId,
          status: application.status,
          notes: application.notes,
          updatedAt: application.updatedAt
        }
      });
    } catch (error) {
      console.error('Update application status error:', error);
      res.status(500).json({
        error: 'Failed to update application status',
        message: 'An error occurred while updating application status'
      });
    }
  }
);

// Get single application details (admin only)
router.get('/applications/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const application = await Application.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'email', 'firstName', 'lastName', 'organization']
        }
      ]
    });

    if (!application) {
      return res.status(404).json({
        error: 'Application not found',
        message: 'The specified application does not exist'
      });
    }

    res.json({
      application
    });
  } catch (error) {
    console.error('Get application details error:', error);
    res.status(500).json({
      error: 'Failed to retrieve application details',
      message: 'An error occurred while retrieving application details'
    });
  }
});

// Complete application with reports (admin only)
router.post(
  '/applications/:id/complete',
  upload.fields([
    { name: 'previewReport', maxCount: 1 },
    { name: 'fullReport', maxCount: 1 },
    { name: 'additionalFiles', maxCount: 10 }
  ]),
  async (req, res) => {
    try {
      const { id } = req.params;
      const { notes, reportPrice, paymentStatus } = req.body;

      const application = await Application.findByPk(id, {
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'email', 'firstName', 'lastName']
          }
        ]
      });

      if (!application) {
        return res.status(404).json({
          error: 'Application not found',
          message: 'The specified application does not exist'
        });
      }

      // Check if preview report is provided
      if (!req.files?.previewReport || req.files.previewReport.length === 0) {
        return res.status(400).json({
          error: 'Preview report required',
          message: 'Please upload a preview report file'
        });
      }

      // Process preview report file
      const previewFile = req.files.previewReport[0];
      const previewReportFile = {
        originalName: previewFile.originalname,
        filename: previewFile.filename,
        path: previewFile.path,
        size: previewFile.size,
        mimeType: previewFile.mimetype,
        uploadedAt: new Date(),
        uploadedBy: req.user.id,
        type: 'preview'
      };

      // Process full report file (optional)
      let fullReportFile = null;
      if (req.files?.fullReport && req.files.fullReport.length > 0) {
        const fullFile = req.files.fullReport[0];
        fullReportFile = {
          originalName: fullFile.originalname,
          filename: fullFile.filename,
          path: fullFile.path,
          size: fullFile.size,
          mimeType: fullFile.mimetype,
          uploadedAt: new Date(),
          uploadedBy: req.user.id,
          type: 'full'
        };
      }

      // Process additional files
      const additionalFiles = [];
      if (req.files?.additionalFiles) {
        for (const file of req.files.additionalFiles) {
          additionalFiles.push({
            originalName: file.originalname,
            filename: file.filename,
            path: file.path,
            size: file.size,
            mimeType: file.mimetype,
            uploadedAt: new Date(),
            uploadedBy: req.user.id,
            type: 'additional'
          });
        }
      }

      // Update application
      application.status = 'completed';
      application.previewReportFile = previewReportFile;
      application.fullReportFile = fullReportFile;
      application.resultFiles = [...(application.resultFiles || []), ...additionalFiles];
      application.processingCompletedAt = new Date();
      application.progressPercentage = 100;
      application.currentStep = 'Completed';

      if (notes) {
        application.notes = notes;
      }

      if (reportPrice) {
        application.reportPrice = parseFloat(reportPrice);
      }

      if (paymentStatus) {
        application.reportPaymentStatus = paymentStatus;
      }

      await application.save();

      res.json({
        message: 'Application completed successfully',
        application: {
          id: application.id,
          applicationId: application.applicationId,
          status: application.status,
          previewReportFile: application.previewReportFile,
          fullReportFile: application.fullReportFile,
          resultFiles: application.resultFiles,
          reportPrice: application.reportPrice,
          reportPaymentStatus: application.reportPaymentStatus,
          processingCompletedAt: application.processingCompletedAt
        }
      });
    } catch (error) {
      console.error('Complete application error:', error);
      res.status(500).json({
        error: 'Failed to complete application',
        message: 'An error occurred while completing the application'
      });
    }
  }
);

// Upload result files for application (admin only)
router.post('/applications/:id/results', upload.array('resultFiles', 10), async (req, res) => {
  try {
    const { id } = req.params;
    const { notes } = req.body;

    const application = await Application.findByPk(id);
    if (!application) {
      return res.status(404).json({
        error: 'Application not found',
        message: 'The specified application does not exist'
      });
    }

    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        error: 'No files uploaded',
        message: 'Please select result files to upload'
      });
    }

    // Process uploaded result files
    const resultFiles = [];
    for (const file of req.files) {
      const resultFile = {
        originalName: file.originalname,
        filename: file.filename,
        path: file.path,
        size: file.size,
        mimeType: file.mimetype,
        uploadedAt: new Date(),
        uploadedBy: req.user.id
      };
      resultFiles.push(resultFile);
    }

    // Update application with result files
    const currentResults = application.resultFiles || [];
    application.resultFiles = [...currentResults, ...resultFiles];

    if (notes) {
      application.notes = notes;
    }

    // If uploading results, likely the analysis is completed
    if (application.status === 'processing') {
      application.status = 'completed';
    }

    await application.save();

    console.log(
      `📁 Admin ${req.user.email} uploaded ${resultFiles.length} result files for application ${application.applicationId}`
    );

    res.json({
      message: `Successfully uploaded ${resultFiles.length} result file(s)`,
      application: {
        id: application.id,
        applicationId: application.applicationId,
        status: application.status,
        resultFiles: application.resultFiles
      }
    });
  } catch (error) {
    console.error('Upload result files error:', error);
    res.status(500).json({
      error: 'Failed to upload result files',
      message: 'An error occurred while uploading result files'
    });
  }
});

// Delete application (admin only)
router.delete('/applications/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const application = await Application.findByPk(id);
    if (!application) {
      return res.status(404).json({
        error: 'Application not found',
        message: 'The specified application does not exist'
      });
    }

    // Soft delete
    application.isDeleted = true;
    application.deletedAt = new Date();
    await application.save();

    console.log(`🗑️ Admin ${req.user.email} deleted application ${application.applicationId}`);

    res.json({
      message: 'Application deleted successfully'
    });
  } catch (error) {
    console.error('Delete application error:', error);
    res.status(500).json({
      error: 'Failed to delete application',
      message: 'An error occurred while deleting application'
    });
  }
});

// Create new user (super admin only)
router.post(
  '/users',
  requireSuperAdmin,
  [
    body('email').isEmail().withMessage('Please provide a valid email'),
    body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
    body('firstName').notEmpty().withMessage('First name is required'),
    body('lastName').notEmpty().withMessage('Last name is required'),
    body('role').isIn(['user', 'admin', 'superadmin']).withMessage('Invalid role'),
    body('organization').optional().isString().withMessage('Organization must be a string'),
    body('phone')
      .optional()
      .isMobilePhone('any')
      .withMessage('Please provide a valid phone number'),
    body('country').optional().isString().withMessage('Country must be a string'),
    body('language')
      .optional()
      .isIn(['en', 'zh', 'es', 'fr', 'de', 'ja', 'ko'])
      .withMessage('Invalid language'),
    body('subscriptionPlan')
      .optional()
      .isIn(['free', 'basic', 'premium'])
      .withMessage('Invalid subscription plan'),
    body('isEmailVerified')
      .optional()
      .isBoolean()
      .withMessage('Email verification status must be boolean'),
    body('isActive').optional().isBoolean().withMessage('Active status must be boolean')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const {
        email,
        password,
        firstName,
        lastName,
        role,
        organization,
        phone,
        country,
        language,
        subscriptionPlan,
        isEmailVerified,
        isActive
      } = req.body;

      // Check if user already exists
      const existingUser = await User.findOne({ where: { email } });
      if (existingUser) {
        return res.status(409).json({
          error: 'User already exists',
          message: 'A user with this email address already exists'
        });
      }

      // Only super admin can create other super admins
      if (role === 'superadmin' && req.user.role !== 'superadmin') {
        return res.status(403).json({
          error: 'Insufficient permissions',
          message: 'Only super administrators can create super admin accounts'
        });
      }

      // Create new user
      const user = await User.create({
        email,
        password, // Will be hashed by the model's beforeCreate hook
        firstName,
        lastName,
        role: role || 'user',
        organization,
        phone,
        country,
        language: language || 'zh',
        subscriptionPlan: subscriptionPlan || 'free',
        isEmailVerified: isEmailVerified !== undefined ? isEmailVerified : false,
        isActive: isActive !== undefined ? isActive : true
      });

      res.status(201).json({
        message: 'User created successfully',
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          organization: user.organization,
          phone: user.phone,
          country: user.country,
          language: user.language,
          subscriptionPlan: user.subscriptionPlan,
          isEmailVerified: user.isEmailVerified,
          isActive: user.isActive,
          createdAt: user.createdAt
        }
      });
    } catch (error) {
      console.error('Create user error:', error);
      res.status(500).json({
        error: 'Failed to create user',
        message: 'An error occurred while creating the user'
      });
    }
  }
);

// Get user details (admin only)
router.get('/users/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByPk(id, {
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'The specified user does not exist'
      });
    }

    res.json({
      user
    });
  } catch (error) {
    console.error('Get user details error:', error);
    res.status(500).json({
      error: 'Failed to get user details',
      message: 'An error occurred while retrieving user details'
    });
  }
});

// Update user (super admin only)
router.patch(
  '/users/:id',
  requireSuperAdmin,
  [
    body('email').optional().isEmail().withMessage('Please provide a valid email'),
    body('firstName').optional().notEmpty().withMessage('First name cannot be empty'),
    body('lastName').optional().notEmpty().withMessage('Last name cannot be empty'),
    body('role').optional().isIn(['user', 'admin', 'superadmin']).withMessage('Invalid role'),
    body('organization').optional().isString().withMessage('Organization must be a string'),
    body('phone')
      .optional()
      .isMobilePhone('any')
      .withMessage('Please provide a valid phone number'),
    body('country').optional().isString().withMessage('Country must be a string'),
    body('language')
      .optional()
      .isIn(['en', 'zh', 'es', 'fr', 'de', 'ja', 'ko'])
      .withMessage('Invalid language'),
    body('subscriptionPlan')
      .optional()
      .isIn(['free', 'basic', 'premium'])
      .withMessage('Invalid subscription plan'),
    body('isEmailVerified')
      .optional()
      .isBoolean()
      .withMessage('Email verification status must be boolean'),
    body('isActive').optional().isBoolean().withMessage('Active status must be boolean'),
    body('emailNotifications')
      .optional()
      .isBoolean()
      .withMessage('Email notifications must be boolean'),
    body('analysisNotifications')
      .optional()
      .isBoolean()
      .withMessage('Analysis notifications must be boolean')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { id } = req.params;
      const updates = req.body;

      // Prevent updating own account through this endpoint
      if (id === req.user.id.toString()) {
        return res.status(400).json({
          error: 'Cannot update own account',
          message: 'Use the profile update endpoint to modify your own account'
        });
      }

      const user = await User.findByPk(id);
      if (!user) {
        return res.status(404).json({
          error: 'User not found',
          message: 'The specified user does not exist'
        });
      }

      // Check if email is being changed and if it already exists
      if (updates.email && updates.email !== user.email) {
        const existingUser = await User.findOne({ where: { email: updates.email } });
        if (existingUser) {
          return res.status(409).json({
            error: 'Email already exists',
            message: 'A user with this email address already exists'
          });
        }
      }

      // Only super admin can change roles to/from super admin
      if (updates.role === 'superadmin' || user.role === 'superadmin') {
        if (req.user.role !== 'superadmin') {
          return res.status(403).json({
            error: 'Insufficient permissions',
            message: 'Only super administrators can modify super admin accounts'
          });
        }
      }

      // Update user
      await user.update(updates);

      res.json({
        message: 'User updated successfully',
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          organization: user.organization,
          phone: user.phone,
          country: user.country,
          language: user.language,
          subscriptionPlan: user.subscriptionPlan,
          isEmailVerified: user.isEmailVerified,
          isActive: user.isActive,
          emailNotifications: user.emailNotifications,
          analysisNotifications: user.analysisNotifications,
          updatedAt: user.updatedAt
        }
      });
    } catch (error) {
      console.error('Update user error:', error);
      res.status(500).json({
        error: 'Failed to update user',
        message: 'An error occurred while updating the user'
      });
    }
  }
);

// Update user role (admin only)
router.patch(
  '/users/:id/role',
  [body('role').isIn(['user', 'admin', 'superadmin']).withMessage('Invalid role')],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { id } = req.params;
      const { role } = req.body;

      // Prevent changing own role
      if (id === req.user.id.toString()) {
        return res.status(400).json({
          error: 'Cannot change own role',
          message: 'You cannot change your own role'
        });
      }

      const user = await User.findByPk(id);
      if (!user) {
        return res.status(404).json({
          error: 'User not found',
          message: 'The specified user does not exist'
        });
      }

      // Only super admin can promote to super admin or demote super admin
      if (role === 'superadmin' || user.role === 'superadmin') {
        if (req.user.role !== 'superadmin') {
          return res.status(403).json({
            error: 'Insufficient permissions',
            message: 'Only super administrators can modify super admin roles'
          });
        }
      }

      user.role = role;
      await user.save();

      res.json({
        message: 'User role updated successfully',
        user: {
          id: user.id,
          email: user.email,
          role: user.role
        }
      });
    } catch (error) {
      console.error('Update user role error:', error);
      res.status(500).json({
        error: 'Failed to update user role',
        message: 'An error occurred while updating user role'
      });
    }
  }
);

// Reset user password (super admin only)
router.patch(
  '/users/:id/reset-password',
  requireSuperAdmin,
  [
    body('newPassword').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
    body('sendEmail').optional().isBoolean().withMessage('Send email must be boolean')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { id } = req.params;
      const { newPassword, sendEmail } = req.body;

      const user = await User.findByPk(id);
      if (!user) {
        return res.status(404).json({
          error: 'User not found',
          message: 'The specified user does not exist'
        });
      }

      // Update password
      user.password = newPassword; // Will be hashed by the model's beforeUpdate hook
      await user.save();

      // TODO: Send email notification if requested
      if (sendEmail) {
        // Implement email notification logic here
        console.log(`Password reset notification should be sent to ${user.email}`);
      }

      res.json({
        message: 'User password reset successfully',
        user: {
          id: user.id,
          email: user.email
        }
      });
    } catch (error) {
      console.error('Reset user password error:', error);
      res.status(500).json({
        error: 'Failed to reset user password',
        message: 'An error occurred while resetting user password'
      });
    }
  }
);

// Delete user account (super admin only)
router.delete('/users/:id', requireSuperAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Prevent deleting own account
    if (id === req.user.id.toString()) {
      return res.status(400).json({
        error: 'Cannot delete own account',
        message: 'You cannot delete your own account'
      });
    }

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'The specified user does not exist'
      });
    }

    // Only super admin can delete super admin accounts
    if (user.role === 'superadmin' && req.user.role !== 'superadmin') {
      return res.status(403).json({
        error: 'Insufficient permissions',
        message: 'Only super administrators can delete super admin accounts'
      });
    }

    // Check if user has active applications
    const activeApplications = await Application.count({
      where: {
        userId: id,
        status: ['pending', 'processing']
      }
    });

    if (activeApplications > 0) {
      return res.status(409).json({
        error: 'Cannot delete user',
        message: `User has ${activeApplications} active applications. Please complete or cancel them first.`
      });
    }

    // Soft delete: deactivate instead of hard delete to preserve data integrity
    await user.update({
      isActive: false,
      email: `deleted_${Date.now()}_${user.email}` // Prevent email conflicts
    });

    res.json({
      message: 'User account deleted successfully',
      user: {
        id: user.id,
        email: user.email,
        isActive: user.isActive
      }
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      error: 'Failed to delete user',
      message: 'An error occurred while deleting user account'
    });
  }
});

// Activate user account
router.patch('/users/:id/activate', async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'The specified user does not exist'
      });
    }

    user.isActive = true;
    await user.save();

    res.json({
      message: 'User account activated successfully',
      user: {
        id: user.id,
        email: user.email,
        isActive: user.isActive
      }
    });
  } catch (error) {
    console.error('Activate user error:', error);
    res.status(500).json({
      error: 'Failed to activate user',
      message: 'An error occurred while activating user account'
    });
  }
});

// Deactivate user account
router.patch('/users/:id/deactivate', async (req, res) => {
  try {
    const { id } = req.params;

    // Prevent deactivating own account
    if (id === req.user.id.toString()) {
      return res.status(400).json({
        error: 'Cannot deactivate own account',
        message: 'You cannot deactivate your own account'
      });
    }

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'The specified user does not exist'
      });
    }

    // Only super admin can deactivate super admin accounts
    if (user.role === 'superadmin' && req.user.role !== 'superadmin') {
      return res.status(403).json({
        error: 'Insufficient permissions',
        message: 'Only super administrators can deactivate super admin accounts'
      });
    }

    user.isActive = false;
    await user.save();

    res.json({
      message: 'User account deactivated successfully',
      user: {
        id: user.id,
        email: user.email,
        isActive: user.isActive
      }
    });
  } catch (error) {
    console.error('Deactivate user error:', error);
    res.status(500).json({
      error: 'Failed to deactivate user',
      message: 'An error occurred while deactivating user account'
    });
  }
});

// System health check
router.get('/system/health', async (req, res) => {
  try {
    // Check database connection
    await sequelize.authenticate();

    // Get system stats
    const uptime = process.uptime();
    const memoryUsage = process.memoryUsage();

    res.json({
      status: 'healthy',
      uptime: Math.floor(uptime),
      memory: {
        used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        total: Math.round(memoryUsage.heapTotal / 1024 / 1024)
      },
      database: 'connected',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('System health check error:', error);
    res.status(500).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Download report files
router.get('/applications/:id/download/:type', async (req, res) => {
  try {
    const { id, type } = req.params;

    const application = await Application.findByPk(id);
    if (!application) {
      return res.status(404).json({
        error: 'Application not found',
        message: 'The specified application does not exist'
      });
    }

    let fileInfo = null;
    if (type === 'preview' && application.previewReportFile) {
      fileInfo = application.previewReportFile;
    } else if (type === 'full' && application.fullReportFile) {
      fileInfo = application.fullReportFile;
    } else {
      return res.status(404).json({
        error: 'File not found',
        message: 'The requested report file does not exist'
      });
    }

    // Check if file exists on disk
    const fs = require('fs');
    const path = require('path');
    const filePath = path.resolve(fileInfo.path);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        error: 'File not found',
        message: 'The report file is not available on the server'
      });
    }

    // Set appropriate headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${fileInfo.originalName}"`);
    res.setHeader('Content-Type', fileInfo.mimeType || 'application/octet-stream');
    res.setHeader('Content-Length', fileInfo.size);

    // Stream the file
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

    fileStream.on('error', (error) => {
      console.error('File stream error:', error);
      if (!res.headersSent) {
        res.status(500).json({
          error: 'File download failed',
          message: 'An error occurred while downloading the file'
        });
      }
    });
  } catch (error) {
    console.error('Download report file error:', error);
    res.status(500).json({
      error: 'Failed to download report file',
      message: 'An error occurred while downloading the report file'
    });
  }
});

module.exports = router;
