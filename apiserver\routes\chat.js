const express = require('express');
const { body, validationResult } = require('express-validator');
const { authenticate, optionalAuth, userRateLimit } = require('../middleware/auth');
const { generateResponse, getSuggestedQuestions } = require('../services/aiService');

const router = express.Router();

// Validation middleware
const validateChatMessage = [
  body('message')
    .notEmpty()
    .withMessage('Message is required')
    .isLength({ max: 1000 })
    .withMessage('Message must be less than 1000 characters'),
  body('language')
    .optional()
    .isIn(['zh', 'en', 'es', 'fr', 'de', 'ja', 'ko'])
    .withMessage('Invalid language code'),
  body('context')
    .optional()
    .isString()
    .isLength({ max: 2000 })
    .withMessage('Context must be less than 2000 characters')
];

// Chat endpoint - requires authentication and rate limiting
router.post(
  '/message',
  authenticate,
  userRateLimit(20, 15 * 60 * 1000), // 20 messages per 15 minutes
  validateChatMessage,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { message, language = 'zh', context } = req.body;

      // Generate AI response
      const result = await generateResponse(message, {
        language: language,
        context: context,
        userId: req.user.id.toString(),
        sessionId: req.headers['x-session-id'] || 'default'
      });

      if (!result.success) {
        return res.status(500).json({
          error: 'AI service error',
          message: result.error.message
        });
      }

      res.json({
        success: true,
        data: result.data
      });
    } catch (error) {
      console.error('Chat message error:', error);
      res.status(500).json({
        error: 'Chat service error',
        message: 'An error occurred while processing your message'
      });
    }
  }
);

// Public chat endpoint with stricter rate limiting
router.post(
  '/public/message',
  optionalAuth,
  [
    // Stricter rate limiting for public access
    (req, res, next) => {
      const clientIP = req.ip || req.connection.remoteAddress;
      // Implement IP-based rate limiting here
      // For now, we'll use a simple in-memory store

      const publicRateLimit = req.app.locals.publicChatRateLimit || new Map();
      const now = Date.now();
      const windowMs = 60 * 60 * 1000; // 1 hour
      const maxRequests = 5; // 5 requests per hour for public users

      const clientKey = req.user ? req.user.id.toString() : clientIP;
      const requests = publicRateLimit.get(clientKey) || [];

      // Clean old requests
      const validRequests = requests.filter((time) => now - time < windowMs);

      if (validRequests.length >= maxRequests) {
        return res.status(429).json({
          error: 'Rate limit exceeded',
          message: 'Too many requests. Please register for higher limits.',
          retryAfter: Math.ceil((validRequests[0] + windowMs - now) / 1000)
        });
      }

      validRequests.push(now);
      publicRateLimit.set(clientKey, validRequests);
      req.app.locals.publicChatRateLimit = publicRateLimit;

      next();
    }
  ],
  validateChatMessage,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { message, language = 'zh', context } = req.body;

      // Generate AI response
      const result = await generateResponse(message, {
        language: language,
        context: context,
        userId: req.user ? req.user.id.toString() : 'anonymous',
        sessionId: req.headers['x-session-id'] || 'public'
      });

      if (!result.success) {
        return res.status(500).json({
          error: 'AI service error',
          message: result.error.message
        });
      }

      // For public users, don't include usage information
      const responseData = { ...result.data };
      if (!req.user) {
        delete responseData.usage;
      }

      res.json({
        success: true,
        data: responseData,
        ...(req.user
          ? {}
          : {
            message: 'Register for unlimited access and advanced features!'
          })
      });
    } catch (error) {
      console.error('Public chat message error:', error);
      res.status(500).json({
        error: 'Chat service error',
        message: 'An error occurred while processing your message'
      });
    }
  }
);

// Get suggested questions
router.get('/suggestions', optionalAuth, async (req, res) => {
  try {
    const language = req.query.language || req.user?.preferences?.language || 'zh';

    const suggestions = getSuggestedQuestions(language);

    res.json({
      suggestions: suggestions,
      language: language
    });
  } catch (error) {
    console.error('Get suggestions error:', error);
    res.status(500).json({
      error: 'Failed to get suggestions',
      message: 'An error occurred while retrieving suggested questions'
    });
  }
});

// Get chat history (for authenticated users)
router.get('/history', authenticate, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const _limit = Math.min(parseInt(req.query.limit) || 20, 100);

    // In a real implementation, you would fetch from a database
    // For now, return empty history
    res.json({
      history: [],
      pagination: {
        currentPage: page,
        totalPages: 0,
        totalCount: 0,
        hasNext: false,
        hasPrev: false
      },
      message: 'Chat history feature will be available soon'
    });
  } catch (error) {
    console.error('Get chat history error:', error);
    res.status(500).json({
      error: 'Failed to get chat history',
      message: 'An error occurred while retrieving chat history'
    });
  }
});

// Clear chat history
router.delete('/history', authenticate, async (req, res) => {
  try {
    // In a real implementation, you would clear from database
    res.json({
      message: 'Chat history cleared successfully'
    });
  } catch (error) {
    console.error('Clear chat history error:', error);
    res.status(500).json({
      error: 'Failed to clear chat history',
      message: 'An error occurred while clearing chat history'
    });
  }
});

// Get chat statistics
router.get('/stats', authenticate, async (req, res) => {
  try {
    // In a real implementation, you would fetch from database
    const stats = {
      totalMessages: 0,
      totalSessions: 0,
      averageResponseTime: 0,
      topTopics: [],
      languageDistribution: {
        zh: 0,
        en: 0,
        others: 0
      }
    };

    res.json({ stats });
  } catch (error) {
    console.error('Get chat stats error:', error);
    res.status(500).json({
      error: 'Failed to get chat statistics',
      message: 'An error occurred while retrieving chat statistics'
    });
  }
});

// Health check for AI service
router.get('/health', async (req, res) => {
  try {
    const { validateConfiguration } = require('../services/aiService');
    const config = validateConfiguration();

    res.json({
      status: config.isValid ? 'healthy' : 'degraded',
      configuration: config,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Chat health check error:', error);
    res.status(500).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

module.exports = router;
