#!/bin/bash

# Quantix Platform Startup Script

set -e

echo "🚀 Starting Quantix Platform..."

# Check if .env file exists
if [ ! -f "apiserver/.env" ]; then
    echo "⚠️  Creating .env file from template..."
    cp apiserver/.env.example apiserver/.env
    echo "📝 Please edit apiserver/.env with your configuration before continuing."
    echo "   Required settings:"
    echo "   - MONGODB_URI"
    echo "   - JWT_SECRET"
    echo "   - EMAIL_USER and EMAIL_PASS"
    echo "   - OPENAI_API_KEY"
    exit 1
fi

# Check if MySQL is running
echo "🔍 Checking MySQL connection..."
if ! mysql --version > /dev/null 2>&1; then
    echo "❌ MySQL is not installed or not in PATH."
    echo "   Please install MySQL first:"
    echo "   macOS: brew install mysql"
    echo "   Ubuntu: sudo apt-get install mysql-server"
    echo "   Or use Docker: docker run -d -p 3306:3306 -e MYSQL_ROOT_PASSWORD=password mysql:8.0"
    exit 1
fi

echo "✅ MySQL is available"

# Install frontend dependencies if needed
if [ ! -d "web/node_modules" ]; then
    echo "📦 Installing frontend dependencies..."
    cd web
    npm install
    cd ..
fi

# Install backend dependencies if needed
if [ ! -d "apiserver/node_modules" ]; then
    echo "📦 Installing backend dependencies..."
    cd apiserver
    npm install
    cd ..
fi

# Create uploads directory
mkdir -p apiserver/uploads

# Setup database
echo "🗄️ Setting up MySQL database..."
node apiserver/scripts/setup-database.js
if [ $? -ne 0 ]; then
    echo "❌ Database setup failed. Please check your MySQL configuration."
    echo "   Make sure MySQL is running and credentials in apiserver/.env are correct."
    exit 1
fi

# Start backend in development mode
echo "🔧 Starting backend server..."
cd apiserver && npm start &
BACKEND_PID=$!
cd ..

# Wait a moment for backend to start
sleep 3

# Check if backend is running
if ! curl -s http://localhost:3001/api/health > /dev/null; then
    echo "❌ Backend failed to start"
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

echo "✅ Backend is running on http://localhost:3001"

# Start frontend in development mode
echo "🎨 Starting frontend server..."
cd web && npm run dev &
FRONTEND_PID=$!
cd ..

# Wait a moment for frontend to start
sleep 3

echo "✅ Frontend is running on http://localhost:5173"

echo ""
echo "🎉 Quantix Platform is now running!"
echo ""
echo "📊 Frontend: http://localhost:5173"
echo "🔧 Backend API: http://localhost:3001/api"
echo "📚 API Documentation: http://localhost:3001/api/health"
echo ""
echo "Press Ctrl+C to stop all services"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping services..."
    kill $BACKEND_PID 2>/dev/null || true
    kill $FRONTEND_PID 2>/dev/null || true
    echo "✅ Services stopped"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for processes
wait
