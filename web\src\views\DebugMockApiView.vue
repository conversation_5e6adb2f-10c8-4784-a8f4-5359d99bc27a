<template>
  <div class="debug-container">
    <header class="debug-header">
      <h1>🔧 Mock API 调试工具</h1>
      <p>用于测试前端功能的模拟API接口</p>
    </header>

    <div class="debug-content">
      <div class="api-section">
        <h2>🔐 认证 API</h2>
        <div class="api-buttons">
          <button @click="testLogin" class="api-btn">测试登录</button>
          <button @click="testRegister" class="api-btn">测试注册</button>
          <button @click="testGetCurrentUser" class="api-btn">获取当前用户</button>
        </div>
      </div>

      <div class="api-section">
        <h2>⚙️ 系统 API</h2>
        <div class="api-buttons">
          <button @click="testCheckInitStatus" class="api-btn">检查初始化状态</button>
          <button @click="testInitializeSystem" class="api-btn">初始化系统</button>
        </div>
      </div>

      <div class="api-section">
        <h2>📋 申请 API</h2>
        <div class="api-buttons">
          <button @click="testGetApplications" class="api-btn">获取申请列表</button>
          <button @click="testSubmitApplication" class="api-btn">提交申请</button>
          <button @click="testDownloadReport" class="api-btn">下载报告</button>
        </div>
      </div>

      <div class="api-section">
        <h2>👥 管理员 API</h2>
        <div class="api-buttons">
          <button @click="testGetDashboardStats" class="api-btn">获取仪表板统计</button>
          <button @click="testGetUsers" class="api-btn">获取用户列表</button>
          <button @click="testCreateUser" class="api-btn">创建用户</button>
        </div>
      </div>

      <div class="api-section">
        <h2>💳 支付 API</h2>
        <div class="api-buttons">
          <button @click="testCreatePayment" class="api-btn">创建支付</button>
          <button @click="testSimulatePayment" class="api-btn">模拟支付成功</button>
        </div>
      </div>

      <div class="results-section">
        <h2>📊 测试结果</h2>
        <div class="results-container">
          <pre v-if="results.length === 0" class="no-results">暂无测试结果</pre>
          <div v-for="(result, index) in results" :key="index" class="result-item">
            <div class="result-header">
              <span class="result-method">{{ result.method }}</span>
              <span class="result-time">{{ result.time }}</span>
              <span :class="['result-status', result.success ? 'success' : 'error']">
                {{ result.success ? '✅ 成功' : '❌ 失败' }}
              </span>
            </div>
            <pre class="result-data">{{ JSON.stringify(result.data, null, 2) }}</pre>
          </div>
        </div>
        <button @click="clearResults" class="clear-btn">清空结果</button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { mockAPI } from '@/services/mockApi'

interface TestResult {
  method: string
  time: string
  success: boolean
  data: any
}

export default defineComponent({
  name: 'DebugMockApiView',
  setup() {
    const results = ref<TestResult[]>([])

    const addResult = (method: string, success: boolean, data: any) => {
      results.value.unshift({
        method,
        time: new Date().toLocaleTimeString(),
        success,
        data,
      })
      // 只保留最近20个结果
      if (results.value.length > 20) {
        results.value = results.value.slice(0, 20)
      }
    }

    const testLogin = async () => {
      try {
        const result = await mockAPI.auth.login({
          email: '<EMAIL>',
          password: 'password123',
        })
        addResult('登录测试', true, result)
      } catch (error) {
        addResult('登录测试', false, error)
      }
    }

    const testRegister = async () => {
      try {
        const result = await mockAPI.auth.register({
          email: '<EMAIL>',
          password: 'password123',
          firstName: 'New',
          lastName: 'User',
        })
        addResult('注册测试', true, result)
      } catch (error) {
        addResult('注册测试', false, error)
      }
    }

    const testGetCurrentUser = async () => {
      try {
        const result = await mockAPI.auth.getCurrentUser()
        addResult('获取当前用户', true, result)
      } catch (error) {
        addResult('获取当前用户', false, error)
      }
    }

    const testCheckInitStatus = async () => {
      try {
        const result = await mockAPI.system.checkInitStatus()
        addResult('检查初始化状态', true, result)
      } catch (error) {
        addResult('检查初始化状态', false, error)
      }
    }

    const testInitializeSystem = async () => {
      try {
        const result = await mockAPI.system.initialize({
          username: 'admin',
          email: '<EMAIL>',
          password: 'admin123',
          firstName: 'Admin',
          lastName: 'User',
        })
        addResult('初始化系统', true, result)
      } catch (error) {
        addResult('初始化系统', false, error)
      }
    }

    const testGetApplications = async () => {
      try {
        const result = await mockAPI.application.getApplications({})
        addResult('获取申请列表', true, result)
      } catch (error) {
        addResult('获取申请列表', false, error)
      }
    }

    const testSubmitApplication = async () => {
      try {
        const result = await mockAPI.application.submitApplication({
          contactInfo: { phone: '************' },
          analysisType: 'protein_complex',
          files: [{ filename: 'test.fasta', originalName: 'test.fasta' }],
          notes: 'Test application',
        })
        addResult('提交申请', true, result)
      } catch (error) {
        addResult('提交申请', false, error)
      }
    }

    const testDownloadReport = async () => {
      try {
        const result = await mockAPI.application.downloadReport(1, 'preview')
        addResult('下载报告', true, { message: 'Mock blob created', size: result.data.size })
      } catch (error) {
        addResult('下载报告', false, error)
      }
    }

    const testGetDashboardStats = async () => {
      try {
        const result = await mockAPI.admin.getDashboardStats()
        addResult('获取仪表板统计', true, result)
      } catch (error) {
        addResult('获取仪表板统计', false, error)
      }
    }

    const testGetUsers = async () => {
      try {
        const result = await mockAPI.admin.getUsers({})
        addResult('获取用户列表', true, result)
      } catch (error) {
        addResult('获取用户列表', false, error)
      }
    }

    const testCreateUser = async () => {
      try {
        const result = await mockAPI.admin.createUser({
          email: '<EMAIL>',
          password: 'password123',
          firstName: 'Test',
          lastName: 'User',
          role: 'user',
        })
        addResult('创建用户', true, result)
      } catch (error) {
        addResult('创建用户', false, error)
      }
    }

    const testCreatePayment = async () => {
      try {
        const result = await mockAPI.payment.createPayment({
          applicationId: 1,
          paymentMethod: 'wechat',
          amount: 99.99,
        })
        addResult('创建支付', true, result)
      } catch (error) {
        addResult('创建支付', false, error)
      }
    }

    const testSimulatePayment = async () => {
      try {
        const result = await mockAPI.payment.simulatePayment('PAY-123456')
        addResult('模拟支付成功', true, result)
      } catch (error) {
        addResult('模拟支付成功', false, error)
      }
    }

    const clearResults = () => {
      results.value = []
    }

    return {
      results,
      testLogin,
      testRegister,
      testGetCurrentUser,
      testCheckInitStatus,
      testInitializeSystem,
      testGetApplications,
      testSubmitApplication,
      testDownloadReport,
      testGetDashboardStats,
      testGetUsers,
      testCreateUser,
      testCreatePayment,
      testSimulatePayment,
      clearResults,
    }
  },
})
</script>

<style scoped>
.debug-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20px;
}

.debug-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.debug-header h1 {
  color: #333;
  margin-bottom: 10px;
}

.debug-header p {
  color: #666;
  margin: 0;
}

.debug-content {
  max-width: 1200px;
  margin: 0 auto;
}

.api-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.api-section h2 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.api-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.api-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s;
}

.api-btn:hover {
  background: #0056b3;
}

.results-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.results-section h2 {
  color: #333;
  margin-bottom: 15px;
}

.results-container {
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  background: #f8f9fa;
}

.no-results {
  text-align: center;
  color: #666;
  font-style: italic;
  margin: 20px 0;
}

.result-item {
  background: white;
  border-radius: 4px;
  margin-bottom: 10px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.result-header {
  background: #f8f9fa;
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e9ecef;
}

.result-method {
  font-weight: bold;
  color: #333;
}

.result-time {
  color: #666;
  font-size: 0.9rem;
}

.result-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
}

.result-status.success {
  background: #d4edda;
  color: #155724;
}

.result-status.error {
  background: #f8d7da;
  color: #721c24;
}

.result-data {
  padding: 10px;
  margin: 0;
  background: white;
  font-size: 0.8rem;
  line-height: 1.4;
  color: #333;
  white-space: pre-wrap;
  word-break: break-all;
}

.clear-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.clear-btn:hover {
  background: #c82333;
}

@media (max-width: 768px) {
  .api-buttons {
    flex-direction: column;
  }

  .api-btn {
    width: 100%;
  }

  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
</style>
