// API Service Selector
// This file decides whether to use real API or mock API based on environment variables

import { 
  authAPI as realAuthAP<PERSON>, 
  userAP<PERSON> as realUserAP<PERSON>,
  uploadAPI as realUploadAPI,
  applicationAP<PERSON> as realApplicationAPI,
  paymentAPI as realPaymentAPI,
  systemAP<PERSON> as realSystemAP<PERSON>,
  admin<PERSON><PERSON> as realAdminAP<PERSON>,
  chat<PERSON><PERSON> as realChatAPI,
  healthAP<PERSON> as realHealthAPI,
  setAuthToken,
  getAuthToken,
  isAuthenticated,
  handleApiError
} from './api.js'

import { 
  mockAuthAPI, 
  mockSystemAPI, 
  mockApplicationAPI, 
  mockUploadAPI, 
  mockAdminAPI, 
  mockPaymentAPI 
} from './mockApi.js'

// Check if we should use mock API
const USE_MOCK_API = import.meta.env.VITE_USE_MOCK_API === 'true'

console.log('🔧 API Service Configuration:', {
  USE_MOCK_API,
  VITE_USE_MOCK_API: import.meta.env.VITE_USE_MOCK_API,
  VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL
})

// Mock implementations for APIs that don't exist in mockApi.js
const mockUserAPI = {
  updateProfile: (data) => {
    console.log("Mock: Update profile", data);
    return Promise.resolve({ data: { message: "Profile updated successfully", user: data } });
  },
  updatePreferences: (data) => {
    console.log("Mock: Update preferences", data);
    return Promise.resolve({ data: { message: "Preferences updated successfully" } });
  },
  changePassword: (data) => {
    console.log("Mock: Change password", data);
    return Promise.resolve({ data: { message: "Password changed successfully" } });
  },
  getStats: () => {
    console.log("Mock: Get user stats");
    return Promise.resolve({ 
      data: { 
        totalApplications: 5,
        completedApplications: 3,
        pendingApplications: 2
      } 
    });
  },
  deactivateAccount: (data) => {
    console.log("Mock: Deactivate account", data);
    return Promise.resolve({ data: { message: "Account deactivated successfully" } });
  },
  deleteAccount: (data) => {
    console.log("Mock: Delete account", data);
    return Promise.resolve({ data: { message: "Account deleted successfully" } });
  }
}

const mockChatAPI = {
  sendMessage: (data) => {
    console.log("Mock: Send chat message", data);
    return Promise.resolve({ 
      data: { 
        message: "This is a mock response to your question about proteins.",
        id: Date.now()
      } 
    });
  },
  sendPublicMessage: (data) => {
    console.log("Mock: Send public chat message", data);
    return Promise.resolve({ 
      data: { 
        message: "This is a mock public response.",
        id: Date.now()
      } 
    });
  },
  getSuggestions: (language) => {
    console.log("Mock: Get chat suggestions", language);
    return Promise.resolve({ 
      data: { 
        suggestions: [
          "What is protein folding?",
          "How do enzymes work?",
          "What are amino acids?"
        ]
      } 
    });
  },
  getChatHistory: (params) => {
    console.log("Mock: Get chat history", params);
    return Promise.resolve({ 
      data: { 
        messages: [],
        pagination: { page: 1, totalPages: 1, total: 0 }
      } 
    });
  },
  clearChatHistory: () => {
    console.log("Mock: Clear chat history");
    return Promise.resolve({ data: { message: "Chat history cleared" } });
  },
  getChatStats: () => {
    console.log("Mock: Get chat stats");
    return Promise.resolve({ 
      data: { 
        totalMessages: 0,
        totalSessions: 0
      } 
    });
  },
  getChatHealth: () => {
    console.log("Mock: Get chat health");
    return Promise.resolve({ 
      data: { 
        status: "healthy",
        aiServiceAvailable: true
      } 
    });
  }
}

const mockHealthAPI = {
  getHealth: () => {
    console.log("Mock: Get health status");
    return Promise.resolve({ 
      data: { 
        status: "OK",
        timestamp: new Date().toISOString(),
        uptime: 3600,
        environment: "development",
        version: "1.0.0"
      } 
    });
  }
}

// Export the appropriate API based on configuration
export const authAPI = USE_MOCK_API ? mockAuthAPI : realAuthAPI
export const userAPI = USE_MOCK_API ? mockUserAPI : realUserAPI
export const uploadAPI = USE_MOCK_API ? mockUploadAPI : realUploadAPI
export const applicationAPI = USE_MOCK_API ? mockApplicationAPI : realApplicationAPI
export const paymentAPI = USE_MOCK_API ? mockPaymentAPI : realPaymentAPI
export const systemAPI = USE_MOCK_API ? mockSystemAPI : realSystemAPI
export const adminAPI = USE_MOCK_API ? mockAdminAPI : realAdminAPI
export const chatAPI = USE_MOCK_API ? mockChatAPI : realChatAPI
export const healthAPI = USE_MOCK_API ? mockHealthAPI : realHealthAPI

// Export utility functions (these are always from the real API)
export { setAuthToken, getAuthToken, isAuthenticated, handleApiError }

// Log which API is being used
if (USE_MOCK_API) {
  console.log('🎭 Using Mock API for development')
} else {
  console.log('🌐 Using Real API')
}
