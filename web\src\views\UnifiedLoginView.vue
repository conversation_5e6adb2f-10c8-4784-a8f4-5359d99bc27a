<template>
  <div class="unified-login-container">
    <div class="login-card">
      <!-- System checking state -->
      <div v-if="checkingSystem" class="checking-state">
        <div class="loading-spinner"></div>
        <h2>{{ $t('checkingSystemStatus') }}</h2>
        <p>{{ $t('verifyingSystemInit') }}</p>
      </div>

      <!-- Login form (only show when system is initialized) -->
      <div v-else-if="systemInitialized">
        <div class="login-header">
          <div class="header-top">
            <h1>🔐 {{ $t('login') }}</h1>
            <div class="language-switcher">
              <select v-model="currentLocale" @change="changeLanguage" class="language-select">
                <option value="en">English</option>
                <option value="zh">中文</option>
                <option value="es">Español</option>
                <option value="fr">Français</option>
                <option value="de">Deutsch</option>
                <option value="ja">日本語</option>
                <option value="ko">한국어</option>
              </select>
            </div>
          </div>
          <p>{{ $t('welcome') }}</p>
        </div>

        <!-- Login Type Selector -->
        <div class="login-type-selector">
        <button
          :class="['type-btn', { active: loginType === 'user' }]"
          @click="loginType = 'user'"
        >
          {{ $t('userLogin') }}
        </button>
        <button
          :class="['type-btn', { active: loginType === 'admin' }]"
          @click="loginType = 'admin'"
        >
          {{ $t('adminLogin') }}
        </button>
      </div>

      <div v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <div v-if="successMessage" class="success-message">
        {{ successMessage }}
      </div>

      <!-- User Login Form -->
      <form v-if="loginType === 'user'" @submit.prevent="handleUserLogin" class="login-form">
        <div class="form-group">
          <label for="user-email">{{ $t('email') }}</label>
          <input
            id="user-email"
            v-model="userForm.email"
            type="email"
            required
            :placeholder="$t('enterYourEmail')"
          />
        </div>

        <div class="form-group">
          <label for="user-password">{{ $t('password') }}</label>
          <input
            id="user-password"
            v-model="userForm.password"
            type="password"
            required
            :placeholder="$t('enterYourPassword')"
          />
        </div>

        <button type="submit" class="login-btn" :disabled="isLoading">
          <span v-if="isLoading">{{ $t('loggingIn') }}</span>
          <span v-else>{{ $t('login') }}</span>
        </button>

        <div class="login-footer">
          <router-link to="/register">{{ $t('noAccountYet') }}</router-link>
        </div>
      </form>

      <!-- Admin Login Form -->
      <form v-if="loginType === 'admin'" @submit.prevent="handleAdminLogin" class="login-form">
        <div class="admin-info">
          <p>{{ $t('adminOnlyAccess') }}</p>
          <p v-if="initInfo.initialized">{{ $t('useAdminAccount') }}</p>
          <p v-else>{{ $t('pleaseUseAdminAccount') }}</p>
        </div>

        <div class="form-group">
          <label for="admin-username">{{ $t('usernameOrEmail') }}</label>
          <input
            id="admin-username"
            v-model="adminForm.username"
            type="text"
            required
            :placeholder="$t('enterAdminUsernameOrEmail')"
          />
        </div>

        <div class="form-group">
          <label for="admin-password">{{ $t('password') }}</label>
          <input
            id="admin-password"
            v-model="adminForm.password"
            type="password"
            required
            :placeholder="$t('enterAdminPassword')"
          />
        </div>

        <button type="submit" class="login-btn" :disabled="isLoading">
          <span v-if="isLoading">{{ $t('loggingIn') }}</span>
          <span v-else>{{ $t('adminLogin') }}</span>
        </button>
      </form>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/counter'
import { authAPI, systemAPI } from '@/services/apiService'
// Dynamic import for systemCheck to avoid bundling conflicts
import { useLanguage } from '@/utils/language'

export default defineComponent({
  name: 'UnifiedLoginView',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const { t } = useI18n()
    const userStore = useUserStore()
    const { locale, changeLanguage: setLanguage } = useLanguage()

    const loginType = ref<'user' | 'admin'>('user')
    const isLoading = ref(false)
    const errorMessage = ref('')
    const successMessage = ref('')

    // 语言切换相关
    const currentLocale = ref(locale.value)

    const changeLanguage = (event: Event) => {
      const target = event.target as HTMLSelectElement
      const newLocale = target.value
      setLanguage(newLocale)
      currentLocale.value = newLocale
    }

    const userForm = reactive({
      email: '',
      password: '',
    })

    const adminForm = reactive({
      username: '',
      password: '',
    })

    const initInfo = reactive({
      email: '',
      username: '',
      initialized: false,
    })

    const systemInitialized = ref(false)
    const checkingSystem = ref(true)

    // Check system initialization status
    onMounted(async () => {
      try {
        const { checkSystemInitialization } = await import('@/utils/systemCheck')
        const systemStatus = await checkSystemInitialization()
        systemInitialized.value = systemStatus.isInitialized

        if (!systemStatus.isInitialized) {
          // System not initialized, redirect to init page
          router.push('/system/init')
          return
        }

        // Check if coming from initialization
        if (route.query.initialized === 'true') {
          loginType.value = 'admin'
          successMessage.value = t('systemInitSuccess')
          initInfo.email = (route.query.email as string) || ''
          initInfo.username = (route.query.username as string) || ''
          initInfo.initialized = true

          if (initInfo.username) {
            adminForm.username = initInfo.username
          }
        }
      } catch (error) {
        console.error('Failed to check system status:', error)
        // If check fails, assume system needs initialization
        router.push('/system/init')
      } finally {
        checkingSystem.value = false
      }
    })

    const handleUserLogin = async () => {
      if (!userForm.email || !userForm.password) {
        errorMessage.value = t('pleaseEnterEmailAndPassword')
        return
      }

      isLoading.value = true
      errorMessage.value = ''

      try {
        const result = await userStore.login({
          email: userForm.email,
          password: userForm.password,
        })

        if (result.success) {
          // User login always goes to console, regardless of role
          // This allows admins to access user interface if needed
          router.push('/console')
        } else {
          errorMessage.value = result.error || t('loginFailed')
        }
      } catch (error) {
        errorMessage.value = t('networkError')
      } finally {
        isLoading.value = false
      }
    }

    const handleAdminLogin = async () => {
      if (!adminForm.username || !adminForm.password) {
        errorMessage.value = t('pleaseEnterUsernameAndPassword')
        return
      }

      isLoading.value = true
      errorMessage.value = ''

      try {
        // Determine if input is email or username
        let loginEmail = adminForm.username
        if (!adminForm.username.includes('@')) {
          if (initInfo.email && adminForm.username === initInfo.username) {
            loginEmail = initInfo.email
          } else if (adminForm.username === 'admin') {
            loginEmail = '<EMAIL>'
          }
        }

        const response = await authAPI.login({
          email: loginEmail,
          password: adminForm.password,
        })

        const { token, user: userData } = response.data

        // Save user info (no role restriction - interface choice determines access)
        userStore.setUser(userData)
        userStore.setToken(token)

        // Redirect to admin interface
        router.push('/admin')
      } catch (error: any) {
        errorMessage.value = error.response?.data?.message || error.message || t('loginFailedCheckCredentials')
      } finally {
        isLoading.value = false
      }
    }

    return {
      loginType,
      isLoading,
      errorMessage,
      successMessage,
      userForm,
      adminForm,
      initInfo,
      systemInitialized,
      checkingSystem,
      handleUserLogin,
      handleAdminLogin,
      // 语言切换相关
      currentLocale,
      changeLanguage,
    }
  },
})
</script>

<style scoped>
.unified-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 450px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.login-header h1 {
  color: #333;
  margin: 0;
  font-size: 2rem;
}

.login-header p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

.language-switcher {
  position: relative;
}

.language-select {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #dee2e6;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
}

.language-select:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.language-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
}

.language-select option {
  background: white;
  color: #333;
}

.login-type-selector {
  display: flex;
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e1e5e9;
}

.type-btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  background: #f8f9fa;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.type-btn.active {
  background: #667eea;
  color: white;
}

.type-btn:hover:not(.active) {
  background: #e9ecef;
}

.admin-info {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  text-align: center;
}

.admin-info p {
  margin: 5px 0;
  color: #666;
  font-size: 0.9rem;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
}

.login-btn {
  width: 100%;
  padding: 14px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.login-btn:hover:not(:disabled) {
  background: #5a6fd8;
}

.login-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.login-footer {
  text-align: center;
  margin-top: 20px;
}

.login-footer a {
  color: #667eea;
  text-decoration: none;
  font-size: 0.9rem;
}

.login-footer a:hover {
  text-decoration: underline;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
}

.success-message {
  background: #d4edda;
  color: #155724;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #c3e6cb;
}

.checking-state {
  text-align: center;
  padding: 40px 20px;
}

.checking-state h2 {
  color: #333;
  margin: 20px 0 10px 0;
  font-size: 1.5rem;
}

.checking-state p {
  color: #666;
  font-size: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
