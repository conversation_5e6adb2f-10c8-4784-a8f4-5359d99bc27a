const request = require('supertest');
const app = require('../index');
const { sequelize, User, Application } = require('../models');

describe('Payment Integration Tests', () => {
  let testUser;
  let authToken;
  let testApplication;

  beforeAll(async () => {
    // 确保数据库连接
    await sequelize.authenticate();
    
    // 运行迁移（如果需要）
    try {
      await sequelize.sync({ force: false });
    } catch (error) {
      console.log('Database sync error (may be expected):', error.message);
    }
  });

  beforeEach(async () => {
    // 创建测试用户
    testUser = await User.create({
      email: `test-${Date.now()}@example.com`,
      password: 'password123',
      firstName: 'Test',
      lastName: 'User',
      isEmailVerified: true
    });

    // 获取认证token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: testUser.email,
        password: 'password123'
      });

    authToken = loginResponse.body.token;

    // 创建测试申请
    testApplication = await Application.create({
      userId: testUser.id,
      contactPhone: '13800138000',
      contactEmail: testUser.email,
      files: [{
        originalName: 'test.pdb',
        filename: 'test.pdb',
        path: '/test/path',
        size: 1024,
        mimetype: 'application/octet-stream'
      }],
      analysisType: 'protein_complex',
      parameters: {
        algorithm: 'alphafold',
        confidence: 0.7
      },
      status: 'completed', // 设置为已完成，可以支付
      reportPrice: 99.99,
      reportPaymentStatus: 'unpaid'
    });
  });

  afterEach(async () => {
    // 清理测试数据 - 先删除申请，再删除用户
    try {
      if (testApplication) {
        await Application.destroy({ where: { userId: testUser.id } });
      }
      if (testUser) {
        await User.destroy({ where: { id: testUser.id } });
      }
    } catch (error) {
      console.log('Cleanup error (may be expected):', error.message);
    }
  });

  afterAll(async () => {
    await sequelize.close();
  });

  describe('Application to Payment Integration', () => {
    it('should create payment for completed application', async () => {
      const paymentData = {
        applicationId: testApplication.id,
        paymentMethod: 'wechat',
        amount: testApplication.reportPrice
      };

      const response = await request(app)
        .post('/api/payments/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(paymentData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.payment).toBeDefined();
      expect(response.body.payment.paymentId).toBeTruthy();
      expect(response.body.payment.amount).toBe(testApplication.reportPrice);
      expect(response.body.payment.paymentMethod).toBe('wechat');
      expect(response.body.payment.status).toBe('pending');
      expect(response.body.payment.qrCodeContent).toBeTruthy();

      // 验证申请记录已更新
      await testApplication.reload();
      expect(testApplication.paymentOrderId).toBeTruthy();
      expect(testApplication.paymentMethod).toBe('wechat');
      expect(testApplication.paymentStatus).toBe('pending');
      expect(testApplication.qrCodeContent).toBeTruthy();
    });

    it('should reject payment for non-completed application', async () => {
      // 更新申请状态为处理中
      await testApplication.update({ status: 'processing' });

      const paymentData = {
        applicationId: testApplication.id,
        paymentMethod: 'alipay',
        amount: testApplication.reportPrice
      };

      const response = await request(app)
        .post('/api/payments/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(paymentData)
        .expect(400);

      expect(response.body.error).toBe('Invalid application status');
    });

    it('should reject payment for already paid application', async () => {
      // 设置申请为已支付
      await testApplication.update({ reportPaymentStatus: 'paid' });

      const paymentData = {
        applicationId: testApplication.id,
        paymentMethod: 'wechat',
        amount: testApplication.reportPrice
      };

      const response = await request(app)
        .post('/api/payments/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(paymentData)
        .expect(400);

      expect(response.body.error).toBe('Already paid');
    });

    it('should reject payment with incorrect amount', async () => {
      const paymentData = {
        applicationId: testApplication.id,
        paymentMethod: 'wechat',
        amount: 50.00 // 错误的金额
      };

      const response = await request(app)
        .post('/api/payments/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(paymentData)
        .expect(400);

      expect(response.body.error).toBe('Invalid amount');
    });
  });

  describe('Payment Status and Lifecycle', () => {
    let paymentId;

    beforeEach(async () => {
      // 创建支付订单
      const paymentData = {
        applicationId: testApplication.id,
        paymentMethod: 'wechat',
        amount: testApplication.reportPrice
      };

      const response = await request(app)
        .post('/api/payments/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(paymentData);

      paymentId = response.body.payment.paymentId;
    });

    it('should get payment status', async () => {
      const response = await request(app)
        .get(`/api/payments/${paymentId}/status`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.payment.paymentId).toBe(paymentId);
      expect(response.body.payment.status).toBe('pending');
      expect(response.body.payment.application).toBeDefined();
    });

    it('should simulate payment success', async () => {
      const response = await request(app)
        .post(`/api/payments/${paymentId}/simulate-success`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.payment.status).toBe('paid');

      // 验证申请状态已更新
      await testApplication.reload();
      expect(testApplication.paymentStatus).toBe('paid');
      expect(testApplication.reportPaymentStatus).toBe('paid');
      expect(testApplication.paidAt).toBeTruthy();
      expect(testApplication.thirdPartyTransactionId).toBeTruthy();
    });

    it('should cancel payment', async () => {
      const response = await request(app)
        .post(`/api/payments/${paymentId}/cancel`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);

      // 验证申请状态已更新
      await testApplication.reload();
      expect(testApplication.paymentStatus).toBe('cancelled');
    });

    it('should get QR code for payment', async () => {
      const response = await request(app)
        .get(`/api/payments/${paymentId}/qrcode`)
        .expect(200);

      expect(response.headers['content-type']).toMatch(/image\/png/);
    });
  });

  describe('Payment History', () => {
    let createdApplications = [];

    beforeEach(async () => {
      createdApplications = [];
      // 创建多个支付记录
      for (let i = 0; i < 3; i++) {
        const app = await Application.create({
          userId: testUser.id,
          contactPhone: '13800138000',
          contactEmail: testUser.email,
          files: [{ originalName: `test${i}.pdb` }],
          analysisType: 'protein_complex',
          status: 'completed',
          reportPrice: 50 + i * 10
        });

        createdApplications.push(app);

        // 初始化支付
        app.initializePayment('wechat', '127.0.0.1', 'test-agent');
        await app.save();

        // 模拟部分支付完成
        if (i % 2 === 0) {
          app.markPaymentPaid(`TXN_${Date.now()}_${i}`, { test: true });
          await app.save();
        }
      }
    });

    afterEach(async () => {
      // 清理创建的申请
      for (const app of createdApplications) {
        try {
          await Application.destroy({ where: { id: app.id } });
        } catch (error) {
          console.log('Cleanup error:', error.message);
        }
      }
      createdApplications = [];
    });

    it('should get payment history', async () => {
      const response = await request(app)
        .get('/api/payments/history')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.payments).toBeInstanceOf(Array);
      expect(response.body.payments.length).toBeGreaterThan(0);
      expect(response.body.pagination).toBeDefined();
      expect(response.body.pagination.total).toBeGreaterThan(0);
    });

    it('should filter payment history by status', async () => {
      const response = await request(app)
        .get('/api/payments/history?status=paid')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.payments).toBeInstanceOf(Array);
      
      // 验证所有返回的支付都是已支付状态
      response.body.payments.forEach(payment => {
        expect(payment.status).toBe('paid');
      });
    });
  });

  describe('Payment Callbacks', () => {
    let paymentId;

    beforeEach(async () => {
      // 创建支付订单
      const paymentData = {
        applicationId: testApplication.id,
        paymentMethod: 'wechat',
        amount: testApplication.reportPrice
      };

      const response = await request(app)
        .post('/api/payments/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(paymentData);

      paymentId = response.body.payment.paymentId;
    });

    it('should handle WeChat payment callback', async () => {
      const callbackData = {
        out_trade_no: paymentId,
        transaction_id: `WX_TXN_${Date.now()}`,
        result_code: 'SUCCESS',
        return_code: 'SUCCESS'
      };

      const response = await request(app)
        .post('/api/payments/callback/wechat')
        .send(callbackData)
        .expect(200);

      expect(response.text).toContain('SUCCESS');

      // 验证申请状态已更新
      await testApplication.reload();
      expect(testApplication.paymentStatus).toBe('paid');
      expect(testApplication.reportPaymentStatus).toBe('paid');
      expect(testApplication.thirdPartyTransactionId).toBe(callbackData.transaction_id);
    });

    it('should handle Alipay payment callback', async () => {
      // 先更新支付方式为支付宝
      await testApplication.update({ 
        paymentMethod: 'alipay',
        thirdPartyOrderId: paymentId 
      });

      const callbackData = {
        out_trade_no: paymentId,
        trade_no: `ALIPAY_TXN_${Date.now()}`,
        trade_status: 'TRADE_SUCCESS'
      };

      const response = await request(app)
        .post('/api/payments/callback/alipay')
        .send(callbackData)
        .expect(200);

      expect(response.text).toBe('success');

      // 验证申请状态已更新
      await testApplication.reload();
      expect(testApplication.paymentStatus).toBe('paid');
      expect(testApplication.reportPaymentStatus).toBe('paid');
      expect(testApplication.thirdPartyTransactionId).toBe(callbackData.trade_no);
    });
  });

  describe('Report Download After Payment', () => {
    beforeEach(async () => {
      // 设置申请为已支付，并添加报告文件
      await testApplication.update({
        reportPaymentStatus: 'paid',
        fullReportFile: {
          originalName: 'full-report.pdf',
          filename: 'full-report.pdf',
          path: '/test/reports/full-report.pdf',
          size: 2048,
          mimetype: 'application/pdf'
        }
      });
    });

    it('should allow download of full report after payment', async () => {
      // 注意：这个测试可能会失败，因为实际文件不存在
      // 但我们可以测试权限检查逻辑
      const response = await request(app)
        .get(`/api/applications/${testApplication.id}/download/full`)
        .set('Authorization', `Bearer ${authToken}`);

      // 应该通过权限检查，但可能因为文件不存在而返回404
      expect([200, 404]).toContain(response.status);
      
      if (response.status === 404) {
        expect(response.body.error).toBe('File not found');
      }
    });

    it('should reject download of full report without payment', async () => {
      // 重置支付状态
      await testApplication.update({ reportPaymentStatus: 'unpaid' });

      const response = await request(app)
        .get(`/api/applications/${testApplication.id}/download/full`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(403);

      expect(response.body.error).toBe('Payment required');
    });
  });
});
