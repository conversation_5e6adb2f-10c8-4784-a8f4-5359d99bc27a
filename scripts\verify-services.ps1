# Quantix Platform Service Verification Script
Write-Host "🔍 Quantix Platform Service Verification" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan
Write-Host ""

# Test Frontend
Write-Host "📱 Testing Frontend Service..." -ForegroundColor Yellow
try {
    $frontend = Invoke-WebRequest -Uri "http://localhost:5173" -UseBasicParsing -TimeoutSec 5
    if ($frontend.StatusCode -eq 200) {
        Write-Host "✅ Frontend: RUNNING (http://localhost:5173)" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Frontend: NOT RUNNING" -ForegroundColor Red
}

# Test Backend Health
Write-Host "🔧 Testing Backend Service..." -ForegroundColor Yellow
try {
    $backend = Invoke-WebRequest -Uri "http://localhost:3001/api/health" -UseBasicParsing -TimeoutSec 5
    if ($backend.StatusCode -eq 200) {
        Write-Host "✅ Backend: RUNNING (http://localhost:3001)" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Backend: NOT RUNNING" -ForegroundColor Red
}

# Test System Status
Write-Host "🏗️ Testing System Initialization Status..." -ForegroundColor Yellow
try {
    $systemStatus = Invoke-WebRequest -Uri "http://localhost:3001/api/system/init-status" -UseBasicParsing -TimeoutSec 5
    $statusData = $systemStatus.Content | ConvertFrom-Json
    
    if ($statusData.isInitialized) {
        Write-Host "✅ System: INITIALIZED ($($statusData.adminCount) admin(s))" -ForegroundColor Green
    } else {
        Write-Host "⚠️ System: NOT INITIALIZED (Ready for setup)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ System Status: UNAVAILABLE" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎯 Quick Access Links:" -ForegroundColor Cyan
Write-Host "   Main Page: http://localhost:5173" -ForegroundColor White
Write-Host "   System Init: http://localhost:5173/system/init" -ForegroundColor White  
Write-Host "   Admin Login: http://localhost:5173/admin/login" -ForegroundColor White
Write-Host ""

# Check if system needs initialization
try {
    $systemCheck = Invoke-WebRequest -Uri "http://localhost:3001/api/system/init-status" -UseBasicParsing -TimeoutSec 5
    $checkData = $systemCheck.Content | ConvertFrom-Json
    
    if (-not $checkData.isInitialized) {
        Write-Host "🚀 NEXT STEP: Initialize your system!" -ForegroundColor Magenta
        Write-Host "   1. Visit: http://localhost:5173/system/init" -ForegroundColor White
        Write-Host "   2. Create admin account (suggested: admin/fHadmin)" -ForegroundColor White
        Write-Host "   3. Login and start using the platform!" -ForegroundColor White
    } else {
        Write-Host "🎉 READY TO USE: System is fully initialized!" -ForegroundColor Magenta
        Write-Host "   Login at: http://localhost:5173/admin/login" -ForegroundColor White
    }
} catch {
    Write-Host "⚠️ Unable to check system status" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
