const { User } = require('../models');

async function upgradeAdminRole() {
  try {
    console.log('🔧 升级管理员角色为超级管理员...');

    // 查找*****************用户
    const adminUser = await User.findOne({
      where: { email: '<EMAIL>' }
    });

    if (!adminUser) {
      console.log('❌ 未找到*****************用户');
      return false;
    }

    console.log(`👤 找到用户: ${adminUser.email}`);
    console.log(`🔍 当前角色: ${adminUser.role}`);
    console.log(`🆔 用户ID: ${adminUser.id}`);

    if (adminUser.role === 'superadmin') {
      console.log('✅ 用户已经是超级管理员');
      return true;
    }

    // 升级角色
    adminUser.role = 'superadmin';
    await adminUser.save();

    console.log('✅ 角色升级成功！');
    console.log(`👤 新角色: ${adminUser.role}`);

    // 验证升级
    const updatedUser = await User.findByPk(adminUser.id);
    console.log(`🔍 验证角色: ${updatedUser.role}`);

    if (updatedUser.role === 'superadmin') {
      console.log('🎉 角色升级验证成功！');
      console.log('');
      console.log('💡 现在管理员拥有以下权限:');
      console.log('   ✅ 添加新用户');
      console.log('   ✅ 删除用户');
      console.log('   ✅ 编辑用户角色');
      console.log('   ✅ 激活/禁用用户');
      console.log('   ✅ 管理所有系统功能');
      console.log('');
      console.log('🌐 请刷新浏览器重新登录以获得新权限');
      return true;
    } else {
      console.log('❌ 角色升级验证失败');
      return false;
    }

  } catch (error) {
    console.error('❌ 升级失败:', error.message);
    return false;
  }
}

// 运行升级
upgradeAdminRole().then(success => {
  if (success) {
    console.log('');
    console.log('🎯 管理员角色升级完成！');
    console.log('🔐 登录信息:');
    console.log('   邮箱: <EMAIL>');
    console.log('   密码: fHadmin');
    console.log('   角色: superadmin');
    console.log('');
    console.log('🌐 管理员登录: http://localhost:80/admin/login');
  } else {
    console.log('');
    console.log('🔧 升级失败，请检查错误信息');
  }

  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ 脚本执行失败:', error.message);
  process.exit(1);
});
