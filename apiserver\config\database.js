const { Sequelize } = require('sequelize');
const path = require('path');
const { getEnvConfig } = require('../utils/env');

// Get parsed environment configuration
const env = getEnvConfig();

// Use SQLite for development if MySQL is not available
const useMySQL = env.USE_MYSQL && env.DB_HOST;

let sequelize;

if (useMySQL) {
  // MySQL configuration
  sequelize = new Sequelize(
    env.DB_NAME,
    env.DB_USER,
    env.DB_PASSWORD,
    {
      host: env.DB_HOST,
      port: env.DB_PORT,
      dialect: 'mysql',
      logging: env.NODE_ENV === 'development' ? console.log : false,
      pool: {
        max: 10,
        min: 0,
        acquire: 30000,
        idle: 10000
      },
      define: {
        timestamps: true,
        underscored: false,
        freezeTableName: true
      }
    }
  );
} else {
  // SQLite configuration (fallback)
  const dbPath = path.join(__dirname, '..', 'database.sqlite');
  sequelize = new Sequelize({
    dialect: 'sqlite',
    storage: dbPath,
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    define: {
      timestamps: true,
      underscored: false,
      freezeTableName: true
    }
  });
}

const connectDB = async () => {
  try {
    await sequelize.authenticate();
    if (useMySQL) {
      console.log('📦 MySQL Connected successfully');
    } else {
      console.log('📦 SQLite Database Connected successfully');
    }

    // Sync database in development or when using MySQL (Docker)
    if (env.NODE_ENV === 'development' || useMySQL) {
      await sequelize.sync({ alter: true });
      console.log('📊 Database synchronized with schema updates');
    }
    
    // 执行兼容性检查
    try {
      const { quickCompatibilityCheck } = require('../utils/compatibility');
      await quickCompatibilityCheck();
    } catch (error) {
      console.error('❌ 兼容性检查失败:', error.message);
      // 不阻止启动，但记录错误
    }

  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    throw error; // Throw error instead of exiting process
  }
};

module.exports = { sequelize, connectDB };
