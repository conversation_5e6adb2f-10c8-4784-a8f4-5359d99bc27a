@echo off
setlocal enabledelayedexpansion

echo 🧪 Testing Complete Quantix System...
echo.

REM Set test environment variables
set NODE_ENV=development
set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=quantix_test
set DB_USER=root
set DB_PASSWORD=
set JWT_SECRET=test-jwt-secret-key-for-testing-only
set EMAIL_SERVICE=gmail
set EMAIL_USER=<EMAIL>
set EMAIL_PASS=test-password
set OPENAI_API_KEY=test-openai-key

echo 📋 Test Configuration:
echo    Database: MySQL (%DB_HOST%:%DB_PORT%/%DB_NAME%)
echo    JWT Secret: %JWT_SECRET%
echo    Email: %EMAIL_USER%
echo.

REM Check if MySQL is available
echo 🔍 Checking MySQL availability...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL is not available. Please install MySQL or use XAMPP/WAMP.
    echo    Download from: https://dev.mysql.com/downloads/mysql/
    echo    Or use XAMPP: https://www.apachefriends.org/
    pause
    exit /b 1
)
echo ✅ MySQL is available

REM Check if dependencies are installed
echo 📦 Checking dependencies...
if not exist "node_modules" (
    echo Installing frontend dependencies...
    npm install
)

if not exist "server\node_modules" (
    echo Installing backend dependencies...
    cd server
    npm install
    cd ..
)

REM Create test database
echo 🗄️ Setting up test database...
cd server
node scripts\setup-database.js
if %errorlevel% neq 0 (
    echo ❌ Database setup failed
    cd ..
    pause
    exit /b 1
)

REM Initialize database schema
echo 📊 Initializing database schema...
node scripts\init-database.js
if %errorlevel% neq 0 (
    echo ❌ Database initialization failed
    cd ..
    pause
    exit /b 1
)

cd ..

echo ✅ Database setup completed

REM Create uploads directory
if not exist "server\uploads" (
    mkdir "server\uploads"
)

REM Test backend API
echo 🔧 Testing backend API...
cd server
timeout /t 2 /nobreak > nul
start "Backend Test" cmd /k "npm run dev"
cd ..

REM Wait for backend to start
echo ⏳ Waiting for backend to start...
timeout /t 10 /nobreak > nul

REM Test API health
echo 🏥 Testing API health...
curl -s http://localhost:3001/api/health > nul
if %errorlevel% neq 0 (
    echo ❌ Backend API is not responding
    echo    Please check the backend terminal for errors
    pause
    exit /b 1
)
echo ✅ Backend API is healthy

REM Test frontend
echo 🎨 Starting frontend...
start "Frontend Test" cmd /k "npm run dev"

echo ⏳ Waiting for frontend to start...
timeout /t 10 /nobreak > nul

echo.
echo 🎉 Complete system test setup completed!
echo.
echo 📊 Frontend: http://localhost:5173
echo 🔧 Backend API: http://localhost:3001/api
echo 📚 API Health: http://localhost:3001/api/health
echo.
echo 🧪 Test the following features:
echo    1. User Registration (http://localhost:5173/register)
echo    2. Email Verification (check console for verification code)
echo    3. User Login (http://localhost:5173/login)
echo    4. File Upload (Dashboard -> Submit New Application)
echo    5. AI Chat (http://localhost:5173/chat)
echo.
echo Press any key to open the application in your browser...
pause > nul

REM Open browser
start http://localhost:5173

echo.
echo ✅ System is running!
echo.
echo To stop the services, close the backend and frontend terminal windows.
echo.
pause
