# Server Configuration
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:5173

# Database (MySQL)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=quantix
DB_USER=root
DB_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRE=7d

# Email Configuration (for verification codes)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# File Upload Configuration
MAX_FILE_SIZE=100MB
UPLOAD_PATH=./server/uploads

# AI Service Configuration (OpenAI or other LLM service)
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-3.5-turbo

# Payment Configuration (for report downloads)
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key

# Analysis Service Configuration
ANALYSIS_SERVICE_URL=http://localhost:8000
ANALYSIS_API_KEY=your-analysis-service-api-key
