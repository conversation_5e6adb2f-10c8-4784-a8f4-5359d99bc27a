
const fs = require('fs');
const path = require('path');

console.log('🔄 恢复Application模型...');

const modelPath = path.join(__dirname, 'models', 'Application.js');
const backupFiles = fs.readdirSync(path.dirname(modelPath))
  .filter(file => file.startsWith('Application.js.backup.'))
  .sort()
  .reverse();

if (backupFiles.length > 0) {
  const latestBackup = path.join(path.dirname(modelPath), backupFiles[0]);
  fs.copyFileSync(latestBackup, modelPath);
  console.log(`✅ 模型已从备份恢复: ${backupFiles[0]}`);
} else {
  console.log('❌ 未找到备份文件');
}
